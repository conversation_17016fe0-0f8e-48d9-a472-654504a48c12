# Backend API Repository

A comprehensive collection of bioinformatics and computational biology web applications and APIs, designed for research and analysis workflows.

## 🧬 Applications

This repository contains multiple specialized applications under the `app/` directory:

### 1. **NeoSeq: Sequence Alignment Tools** 🔬
*Location: `app/sequence_alignment/`*

A comprehensive web-based application suite for sequence alignment analysis, offering both FASTQ file processing and individual sequence comparison capabilities.

#### Features:
- **🧬 FASTQ File Alignment**
  - Upload FASTQ files via drag-and-drop or file browser
  - Align multiple sequences with custom reference sequences
  - Quality score analysis with Phred scores
  - Automatic reverse complement detection
  - Summary statistics and detailed results
  - Download results as JSON

- **🔀 Two-Sequence Alignment**
  - Direct input of two individual sequences (DNA, RNA, or protein)
  - Customizable alignment parameters (match/mismatch scores, gap penalties)
  - Real-time mutation, insertion, and deletion highlighting
  - Interactive parameter tuning
  - Detailed alignment visualization with color coding

#### Quick Start:
```bash
cd app/sequence_alignment
pip install -r requirements.txt
python run_server.py
# Access at http://localhost:5000
```

#### API Endpoints:
- `GET /` - Main page with functionality overview
- `GET /fastq-alignment` - FASTQ file alignment interface
- `GET /two-sequence-alignment` - Two-sequence alignment interface
- `POST /align` - FASTQ alignment endpoint
- `POST /align-two-sequences` - Two-sequence alignment endpoint

---

### 2. **MD YAML Generator** ⚛️
*Location: `app/md_yaml_generator/`*

A simple web interface for generating YAML configuration files for molecular dynamics simulations.

#### Features:
- **🔧 Simulation Configuration**
  - Configure basic simulation parameters (method, steps, temperature)
  - Set up integrator and barostat settings
  - Multiple simulation methods: Equilibration, Minimization, Metadynamics

- **🎯 Restraint Management**
  - Add and configure multiple restraints
  - Distance reference position restraints
  - Distance restraints between groups

- **📁 File Management**
  - Specify input and output file paths
  - Generate and download YAML configuration files
  - Web-based interface for easy configuration

#### Quick Start:
```bash
cd app/md_yaml_generator
python -m http.server 8000
# Access at http://localhost:8000
```

## 🚀 Deployment

### Kubernetes Deployment

Both applications are configured for Kubernetes deployment with dedicated YAML configurations in the `deploy/` directory.

#### NeoSeq Sequence Alignment:
```bash
kubectl apply -f deploy/seq-alignment.yaml
```

**Access URLs:**
- Main Application: `https://api.neobinder-internal.com/neoseq/alignment/`
- FASTQ Alignment: `https://api.neobinder-internal.com/neoseq/alignment/fastq-alignment`
- Two-Sequence Alignment: `https://api.neobinder-internal.com/neoseq/alignment/two-sequence-alignment`

## 🛠️ Development

### Prerequisites
- Python 3.8+
- pip or conda for package management
- Docker (for containerized deployment)
- Kubernetes cluster (for production deployment)

### Local Development Setup

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd backend-api
   ```

2. **Set up individual applications:**
   ```bash
   # For NeoSeq Sequence Alignment
   cd app/sequence_alignment
   pip install -r requirements.txt
   python run_server.py
   
   # For MD YAML Generator
   cd app/md_yaml_generator
   python -m http.server 8000
   ```

### Project Structure
```
backend-api/
├── app/
│   ├── sequence_alignment/          # NeoSeq Sequence Alignment Tools
│   │   ├── main.py                 # FastAPI backend
│   │   ├── run_server.py           # Server startup script
│   │   ├── requirements.txt        # Python dependencies
│   │   ├── static/                 # Frontend assets
│   │   └── README.md              # Detailed documentation
│   │
│   ├── md_yaml_generator/          # MD YAML Generator
│   │   ├── main.py                # Backend logic
│   │   ├── index.html             # Web interface
│   │   ├── script.js              # Frontend JavaScript
│   │   ├── styles.css             # Styling
│   │   └── README.md              # Application documentation
│   │
│   ├── api.py                     # Shared API utilities
│   └── align.py                   # Alignment utilities
│
├── deploy/
│   └── seq-alignment.yaml         # Kubernetes deployment config
│
└── README.md                      # This file
```

## 📊 Use Cases

### Research Applications:
- **Genomics Research**: FASTQ file analysis and sequence comparison
- **Molecular Dynamics**: Simulation configuration and setup
- **Bioinformatics Pipelines**: Automated sequence alignment workflows
- **Educational**: Teaching sequence alignment concepts

### Production Features:
- **Scalable**: Kubernetes-ready with horizontal scaling
- **Robust**: Health checks and error handling
- **User-Friendly**: Intuitive web interfaces
- **Configurable**: Customizable parameters for different use cases


## 📄 License

This project is part of the NeoBinder platform for computational biology and bioinformatics research.

---

**🔬 Built for researchers, by researchers** | **⚡ Optimized for performance** | **🌐 Ready for production**
