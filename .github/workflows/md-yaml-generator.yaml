name: image-build

on:
  workflow_dispatch: null
env:
  APP_NAME: "backend-api"
  ACR_ZONE: ${{ vars.ACR_ZONE }}
  ACR_DOMAIN:  ${{ vars.ACR_DOMAIN }}
  IMAGE_TAG: ${{ vars.ACR_DOMAIN }}/${{ vars.ACR_ZONE }}/backend-api:latest
jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Docker ACR # 登录到 Docker 镜像仓库
        run: echo "${{ secrets.ACR_PASSWORD }}" | docker login --username=${{ secrets.ACR_USERNAME }} ${{ env.ACR_DOMAIN }} --password-stdin

      - name: Build Docker Image
        uses: docker/build-push-action@v6
        with:
          context: "."
          dockerfile: "Dockerfile"
          tags: ${{ env.IMAGE_TAG }}
      - name: Push Docker image
        run: docker push ${{ env.IMAGE_TAG }}

