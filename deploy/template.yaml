---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-backend
  namespace: api
  labels:
    k8s-app: web-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: web-backend
  template:
    metadata:
      labels:
        k8s-app: web-backend
    spec:
      nodeSelector:
        worktype: worker
      containers:
      - name: api-bin
        image: harbor.neobinder-internal.com/api/backend:20240802
          # command: ["/opt/conda/bin/uvicorn","main:app","--host","0.0.0.0","--root-path","/backend"]
        command: ["/opt/conda/bin/uvicorn","main:app","--host","0.0.0.0"]
        ports:
        - containerPort: 8000
          name: web
          protocol: TCP
        imagePullPolicy: Always
      imagePullSecrets:
      - name: regcred
---
apiVersion: v1
kind: Service
metadata:
  name: web-backend-service
  namespace: api
  labels:
    k8s-app: web-backend
spec:
  selector:
    k8s-app: web-backend
  ports:
  - name: web
    port: 8000
    protocol: TCP
