---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: neoseq-alignment
  namespace: api
  labels:
    k8s-app: neoseq-alignment
    app: neoseq
    component: sequence-alignment
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: neoseq-alignment
  template:
    metadata:
      labels:
        k8s-app: neoseq-alignment
        app: neoseq
        component: sequence-alignment
    spec:
      nodeSelector:
        worktype: worker
      containers:
      - name: neoseq-alignment
        image: harbor.neobinder-internal.com/api/backend-api:latest
        imagePullPolicy: Always
        workingDir: /app/app/sequence_alignment
        # command: ["/app/.venv/bin/python3", "run_server.py"]
        command: ["/app/.venv/bin/uvicorn","main:app","--host","0.0.0.0","--port","5000"]
        volumeMounts:
        - mountPath: /dataset
          name: nr-dataset
        - mountPath: /joblogs
          name: results-storage
        ports:
        - containerPort: 5000
          name: web
          protocol: TCP
        env:
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "5000"
        - name: REDIS_URL
          value: "redis://neoseq-redis-service:6379/0"
        - name: BINDIR
          value: "/app/bin"
        - name: BLASTDB
          value: "/dataset"
        - name: BLAST_RESULTS_DIR
          value: "/joblogs/blast_results"
        - name: FASTP_RESULTS_DIR
          value: "/joblogs/fastp_results"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: nr-dataset
        nfs:
          server: nfs01.neobinder-internal.com
          path: /mnt/bigbucket/dataset/protein/blastdb
          readOnly: true
      - name: results-storage
        persistentVolumeClaim:
          claimName: neoseq-results-pvc
      imagePullSecrets:
      - name: regcred
---
apiVersion: v1
kind: Service
metadata:
  name: neoseq-alignment-service
  namespace: api
  labels:
    k8s-app: neoseq-alignment
    app: neoseq
    component: sequence-alignment
spec:
  selector:
    k8s-app: neoseq-alignment
  ports:
  - name: web
    port: 5000
    targetPort: 5000
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: neoseq-alignment-ingress
  namespace: api
  labels:
    k8s-app: neoseq-alignment
    app: neoseq
    component: sequence-alignment
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    cert-manager.io/issuer: letsencrypt-production
    cert-manager.io/issuer-kind: ClusterIssuer
spec:
  ingressClassName: nginx
  rules:
  - host: api.neobinder-internal.com
    http:
      paths:
      - backend:
          service:
            name: neoseq-alignment-service
            port:
              number: 5000
        path: /neoseq/?(.*)
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - api.neobinder-internal.com
    secretName: api-ingress
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: neoseq-redis
  namespace: api
  labels:
    app: neoseq
    component: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: neoseq
      component: redis
  template:
    metadata:
      labels:
        app: neoseq
        component: redis
    spec:
      containers:
      - name: redis
        image: harbor.neobinder-internal.com/docker/library/redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command: ["redis-server"]
        args: ["--port", "6379", "--appendonly", "yes"]
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        emptyDir: {}
      imagePullSecrets:
      - name: regcred
---
apiVersion: v1
kind: Service
metadata:
  name: neoseq-redis-service
  namespace: api
  labels:
    app: neoseq
    component: redis
spec:
  selector:
    app: neoseq
    component: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    protocol: TCP
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: neoseq-celery-worker
  namespace: api
  labels:
    app: neoseq
    component: celery-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: neoseq
      component: celery-worker
  template:
    metadata:
      labels:
        app: neoseq
        component: celery-worker
    spec:
      nodeSelector:
        worktype: worker
      containers:
      - name: celery-worker
        image: harbor.neobinder-internal.com/api/backend-api:latest
        imagePullPolicy: Always
        workingDir: /app/app/sequence_alignment
        command: ["/app/.venv/bin/celery", "-A", "celery_app", "worker", "--loglevel=info", "--concurrency=2"]
        env:
        - name: REDIS_URL
          value: "redis://neoseq-redis-service:6379/0"
        - name: BINDIR
          value: "/app/bin"
        - name: BLASTDB
          value: "/dataset"
        - name: BLAST_RESULTS_DIR
          value: "/joblogs/blast_results"
        - name: FASTP_RESULTS_DIR
          value: "/joblogs/fastp_results"
        volumeMounts:
        - mountPath: /dataset
          name: nr-dataset
        - mountPath: /joblogs
          name: results-storage
        resources:
          requests:
            memory: "8Gi"
            cpu: "6000m"
          limits:
            memory: "16Gi"
            cpu: "8000m"
      volumes:
      - name: nr-dataset
        nfs:
          server: nfs01.neobinder-internal.com
          path: /mnt/bigbucket/dataset/protein/blastdb
          readOnly: true
      - name: results-storage
        persistentVolumeClaim:
          claimName: neoseq-results-pvc
      imagePullSecrets:
      - name: regcred
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: neoseq-alignment-config
  namespace: api
  labels:
    app: neoseq
    component: sequence-alignment
data:
  # Application configuration
  app_name: "NeoSeq Sequence Alignment Tools"
  app_description: "Comprehensive bioinformatics tools for sequence analysis and alignment"

  # Default alignment parameters
  default_match_score: "1.0"
  default_mismatch_score: "-1.0"
  default_open_gap_score: "-2.0"
  default_extend_gap_score: "-0.5"

  # File upload limits
  max_file_size: "100MB"

  # Performance settings
  alignment_timeout: "300"
  max_concurrent_alignments: "5"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: neoseq-results-pvc
  namespace: api
  labels:
    app: neoseq
    component: storage
spec:
  storageClassName: longhorn
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 20Gi
  
