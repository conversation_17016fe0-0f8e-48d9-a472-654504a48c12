---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yaml-generator
  namespace: api
  labels:
    k8s-app: yaml-generator
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: yaml-generator
  template:
    metadata:
      labels:
        k8s-app: yaml-generator
    spec:
      nodeSelector:
        worktype: worker
      containers:
      - name: api-bin
        image: harbor.neobinder-internal.com/api/backend-api:250508
        workingDir: /app/app/md_yaml_generator
        command: ["/app/.venv/bin/python3","-m", "http.server"]
        ports:
        - containerPort: 8000
          name: web
          protocol: TCP
        imagePullPolicy: Always
      imagePullSecrets:
      - name: regcred
---
apiVersion: v1
kind: Service
metadata:
  name: yaml-generator-service
  namespace: api
  labels:
    k8s-app: yaml-generator
spec:
  selector:
    k8s-app: yaml-generator
  ports:
  - name: web
    port: 8000
    protocol: TCP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: yaml-generator-ingress
  namespace: api
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/issuer:  letsencrypt-production
    cert-manager.io/issuer-kind: ClusterIssuer
spec:
  ingressClassName: nginx
  rules:
  - host: api.neobinder-internal.com
    http:
      paths:
      - backend:
          service:
            name: yaml-generator-service
            port:
              number: 8000
        path: /md-generator/(.*)
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - api.neobinder-internal.com
    secretName: api-ingress
