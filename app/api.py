import uuid

from fastapi import Fast<PERSON><PERSON>
from fastapi.responses import PlainTextResponse



app = FastAPI(root_path="/backend")

@app.get("/", response_class=PlainTextResponse)
def help():
    content = """Welcome to neobinder backend api
    /generate   method:get 
    /generate/uuid   method:get
    /generate/uuid/short   method:get
    /generate/uuid/long   method:get"""
    return content


@app.get("/generate/uuid/long", response_class=PlainTextResponse)
@app.get("/generate/uuid", response_class=PlainTextResponse)
def gen_long_uuid():
    return str(uuid.uuid4())

@app.get("/generate/uuid/short", response_class=PlainTextResponse)
def gen_short_uuid():
    uuid_str = str(uuid.uuid4())
    return str(uuid_str.split("-")[-1])




if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", port=5000, log_level="debug", host="0.0.0.0")
    #  flask_app.run(port=5000, host="0.0.0.0")