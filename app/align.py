import os
from pydantic import BaseModel
from io import StringIO
import tempfile

from Bio import SeqIO
from Bio.Align import <PERSON>scleCommandline
from fastapi import FastAPI, UploadFile
from fastapi.responses import PlainTextResponse, JSONResponse

app = FastAPI(root_path="/binary")
BINDIR = os.environ.get("BINDIR", "/workspace/app/bin")
muscle_exe = os.path.join(BINDIR, "muscle3.8")

@app.get("/", response_class=PlainTextResponse)
def help():
    content = """Welcome to neobinder binary api
    /muscle   method:Post """
    return content


class FastaInput(BaseModel):
    fasta: str


@app.post("/sequences/muscle/input")
def muscle_alignment(fasta_conf: FastaInput):
    """Align 2 sequences with muscle"""
    fasta = fasta_conf.fasta
    sequences = list(SeqIO.parse(StringIO(fasta), "fasta"))
    temp = tempfile.NamedTemporaryFile()
    SeqIO.write(sequences, temp.name, "fasta")
    name = os.path.splitext(temp.name)[0]
    cline = MuscleCommandline(muscle_exe, input=temp.name, out=name + '.txt')
    stdout, stderr = cline()
    with open(temp.name + ".txt", "r") as f:
        content = f.read()
    return JSONResponse({"align": content})


@app.post("/sequences/muscle/file")
def muscle_fasta_alignment(fasta_file: UploadFile):
    temp = tempfile.NamedTemporaryFile()
    temp.write(fasta_file.content)
    cline = MuscleCommandline(muscle_exe,
                              input=temp.name,
                              out=temp.name + ".txt")
    with open(temp.name + ".txt", "r") as f:
        content = f.read()
    return JSONResponse({"align": content})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", port=5000, log_level="debug", host="0.0.0.0")
    #  flask_app.run(port=5000, host="0.0.0.0")
