from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Form, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
# from typing import List, Dict, Any, Generator
from Bio import SeqIO, Align
# from Bio.Seq import Seq
import io
import uuid
import subprocess
import tempfile
import os
import json
import csv
import uuid
import io
from datetime import datetime
import xml.etree.ElementTree as ET
from pathlib import Path
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()
from celery_app import celery_app
from blast_tasks import run_blast_search
from blast_storage import blast_storage
from job_metadata_storage import JobMetadataStorage
from fastp_storage import fastp_storage

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# In-memory storage for sequence details (for demo purposes)
# In production, this should be stored in a database
sequence_details_store = {}

# Check for MUSCLE availability
BINDIR = os.environ.get("BINDIR", "/app/bin")
MUSCLE_PATH = os.path.join(BINDIR, "muscle3.8")
try:
    subprocess.run([MUSCLE_PATH, '-version'], capture_output=True, check=True)
    MUSCLE_AVAILABLE = True
    logger.info(f"MUSCLE found at: {MUSCLE_PATH}")
except (subprocess.CalledProcessError, FileNotFoundError):
    MUSCLE_AVAILABLE = False
    logger.warning("MUSCLE not found. Multiple sequence alignment will not be available.")

app = FastAPI(title="Sequence Alignment Tool", description="Upload FASTQ files and align with reference sequences")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# BLAST-related models
class BlastParameters(BaseModel):
    evalue: float = 10.0
    max_target_seqs: int = 100
    word_size: int = 5
    matrix: str = "BLOSUM62"
    gap_open: int = 11
    gap_extend: int = 1
    database: str = "refseq_protein"

class BlastQuery(BaseModel):
    sequence: str
    job_name: Optional[str] = None
    # email: Optional[str] = None
    parameters: Optional[BlastParameters] = None

# Configuration for BLAST results storage
BLAST_RESULTS_DIR = os.getenv("BLAST_RESULTS_DIR", "./data/blast_results")

# Configuration for FASTQ results storage
FASTP_RESULTS_DIR = os.getenv("FASTP_RESULTS_DIR", "./data/fastp_results")

# Load existing jobs from persistent storage on startup
print("Loading existing BLAST jobs from persistent storage...")
blast_job_storage = JobMetadataStorage(BLAST_RESULTS_DIR)
blast_jobs_db = blast_job_storage.load_all_jobs()
print(f"Loaded {len(blast_jobs_db)} existing jobs from storage")

# Initialize FASTQ job storage
print("Initializing FASTQ job storage...")
fastp_job_storage = JobMetadataStorage(FASTP_RESULTS_DIR)
fastp_jobs_db = fastp_job_storage.load_all_jobs()
print(f"Loaded {len(fastp_jobs_db)} existing FASTQ jobs from storage")




def get_mutation_positions_and_alignment(seq1, seq2, aligner):
    """
    Get mutation positions between two sequences using alignment
    Returns tuple of (mutations_list, alignment_data)
    mutations_list: list of (position, ref_base, alt_base) tuples
    alignment_data: dict with aligned sequences and mutation positions for display
    """
    alignment = aligner.align(seq1, seq2)[0]  # Get best alignment
    mutations = []

    # Get aligned sequences
    aligned_seq1 = str(alignment[0])
    aligned_seq2 = str(alignment[1])

    # Find the start and end positions to skip leading and trailing gaps in seq1
    start_pos = 0
    end_pos = len(aligned_seq1)

    # Find first non-gap position in aligned_seq1
    while start_pos < len(aligned_seq1) and aligned_seq1[start_pos] == '-':
        start_pos += 1

    # Find last non-gap position in aligned_seq1
    while end_pos > start_pos and aligned_seq1[end_pos - 1] == '-':
        end_pos -= 1

    # Track position in original sequence (excluding gaps)
    pos_in_ref = 1  # 1-based positioning
    mutation_positions_in_alignment = []  # Track positions in the alignment for highlighting
    insertion, deletion = False, False
    for i in range(start_pos, end_pos):
        base1 = aligned_seq1[i]
        base2 = aligned_seq2[i]

        if base1 != '-' and base2 != '-':
            # Both sequences have bases - check for mismatch
            if base1 != base2:
                mutations.append((pos_in_ref, base1, base2))
                mutation_positions_in_alignment.append(i)
            pos_in_ref += 1
        elif base1 != '-' and base2 == '-':
            # Reference has base, query has gap - deletion
            deletion = True
            pos_in_ref += 1
        elif base1 == '-' and base2 != '-':
            # Reference has gap, query has base - insertion
            insertion = True

    # Create alignment data for display
    alignment_data = {
        'aligned_seq1': aligned_seq1,
        'aligned_seq2': aligned_seq2,
        'mutation_positions': mutation_positions_in_alignment
    }

    return mutations, alignment_data, insertion, deletion


def generate_mutation_analysis(sequences):
    """
    Generate comprehensive mutation analysis from sequence results
    """
    mutation_counts = {}
    total_mutations = 0
    sequences_with_mutations = 0
    mutation_patterns = {}

    # Analyze each sequence
    for seq in sequences:
        if seq.get('mutations') and len(seq['mutations']) > 0:
            sequences_with_mutations += 1

            # Create sorted mutation pattern for grouping
            mutation_pattern = ','.join(sorted([m['mutation'] for m in seq['mutations']]))

            if mutation_pattern not in mutation_patterns:
                mutation_patterns[mutation_pattern] = {
                    'mutations': sorted([m['mutation'] for m in seq['mutations']]),
                    'sequences': [],
                    'count': 0
                }

            mutation_patterns[mutation_pattern]['sequences'].append({
                'id': seq['id'],
                'score': seq['alignment_score'],
                'length': seq['length']
            })
            mutation_patterns[mutation_pattern]['count'] += 1

            # Count individual mutations
            for mut in seq['mutations']:
                mutation_key = mut['mutation']
                mutation_counts[mutation_key] = mutation_counts.get(mutation_key, 0) + 1
                total_mutations += 1

    # Sort mutations by frequency (top 10)
    sorted_mutations = sorted(mutation_counts.items(), key=lambda x: x[1], reverse=True)[:10]

    # Sort mutation patterns by frequency (top 10)
    sorted_patterns = sorted(
        [(pattern, data) for pattern, data in mutation_patterns.items()],
        key=lambda x: x[1]['count'],
        reverse=True
    )[:10]

    # Calculate statistics
    total_sequences = len(sequences)
    mutation_rate = (sequences_with_mutations / total_sequences * 100) if total_sequences > 0 else 0
    avg_mutations_per_sequence = (total_mutations / total_sequences) if total_sequences > 0 else 0

    return {
        'statistics': {
            'total_mutations': total_mutations,
            'sequences_with_mutations': sequences_with_mutations,
            'mutation_rate': round(mutation_rate, 1),
            'avg_mutations_per_sequence': round(avg_mutations_per_sequence, 2),
            'total_sequences': total_sequences
        },
        'top_mutations': [
            {
                'mutation': mutation,
                'count': count,
                'percentage': round((count / total_sequences * 100), 1),
                'rank': idx + 1
            }
            for idx, (mutation, count) in enumerate(sorted_mutations)
        ],
        'top_patterns': [
            {
                'pattern': pattern,
                'mutations': data['mutations'],
                'count': data['count'],
                'percentage': round((data['count'] / total_sequences * 100), 1),
                'example_sequences': data['sequences'][:3],  # First 3 examples
                'rank': idx + 1
            }
            for idx, (pattern, data) in enumerate(sorted_patterns)
        ]
    }


def process_fastq_alignment(fastq_content, reference_sequence, match_score=1.0, mismatch_score=-1.0, open_gap_score=-2.0, extend_gap_score=-0.5):
    """
    Process FASTQ file and align sequences with reference
    """
    # Setup aligner with custom parameters
    aligner = Align.PairwiseAligner()
    aligner.mode = 'global'
    aligner.match_score = match_score
    aligner.mismatch_score = mismatch_score
    aligner.open_gap_score = open_gap_score
    aligner.extend_gap_score = extend_gap_score

    # Parse FASTQ content
    fastq_io = io.StringIO(fastq_content)
    records = list(SeqIO.parse(fastq_io, "fastq"))

    results = []

    for i, record in enumerate(records):
        # Calculate quality metrics
        avg_quality = sum(record.letter_annotations['phred_quality']) / len(record.letter_annotations['phred_quality'])
        min_quality = min(record.letter_annotations['phred_quality'])
        max_quality = max(record.letter_annotations['phred_quality'])
        
        # Try both forward and reverse complement
        alignment_forward = aligner.align(record.seq, reference_sequence)
        alignment_reverse = aligner.align(record.seq.reverse_complement(), reference_sequence)
        
        # Choose the better alignment
        if alignment_forward.score > alignment_reverse.score:
            is_reverse = False
            alignment = alignment_forward
            query_seq = str(record.seq)
        else:
            is_reverse = True
            alignment = alignment_reverse
            query_seq = str(record.seq.reverse_complement())
        
        # Get mutations and alignment data
        mutations, alignment_data, insertion, deletion = get_mutation_positions_and_alignment(reference_sequence, query_seq, aligner)

        # Calculate GC content
        sequence_str = str(record.seq)
        gc_count = sequence_str.count('G') + sequence_str.count('C')
        gc_content = round((gc_count / len(sequence_str)) * 100, 2) if len(sequence_str) > 0 else 0

        # Get quality scores
        quality_scores = record.letter_annotations.get('phred_quality', [])

        # Calculate identity percentage
        total_positions = len(alignment_data['aligned_seq1'])
        matches = sum(1 for i in range(total_positions)
                     if alignment_data['aligned_seq1'][i] == alignment_data['aligned_seq2'][i]
                     and alignment_data['aligned_seq1'][i] != '-')
        identity_percent = round((matches / total_positions) * 100, 2) if total_positions > 0 else 0

        result = {
            'id': record.id,
            'length': len(record.seq),
            'avg_quality': round(avg_quality, 2),
            'min_quality': min_quality,
            'max_quality': max_quality,
            'alignment_score': alignment.score,
            'identity_percent': identity_percent,
            'is_reverse_complement': is_reverse,
            'num_mutations': len(mutations),
            'mutations': [{'position': pos, 'reference': ref, 'sequence': alt, 'mutation': f"{ref}{pos}{alt}"}
                         for pos, ref, alt in mutations],  # DO NOT Limit to first 20 mutations for display
            'insertions': [{'position': i, 'sequence': alignment_data['aligned_seq2'][i]}
                          for i in alignment_data['mutation_positions']
                          if alignment_data['aligned_seq1'][i] == '-'] if insertion else [],
            'deletions': [{'position': i, 'sequence': alignment_data['aligned_seq1'][i]}
                         for i in alignment_data['mutation_positions']
                         if alignment_data['aligned_seq2'][i] == '-'] if deletion else [],
            'insertion': insertion,
            'deletion': deletion,
            'alignment': alignment_data,  # Include alignment data for display
            'alignment_data': alignment_data,  # Alternative key for compatibility
            'full_sequence': sequence_str,  # Include original full sequence
            'gc_content': gc_content,  # Include GC content
            'quality_scores': quality_scores  # Include quality scores
        }
        results.append(result)

    
    # Summary statistics
    alignment_scores = [r['alignment_score'] for r in results]
    mutation_counts = [r['num_mutations'] for r in results]
    
    summary = {
        'total_sequences': len(results),
        'avg_alignment_score': round(sum(alignment_scores) / len(alignment_scores), 2) if alignment_scores else 0,
        'max_alignment_score': max(alignment_scores) if alignment_scores else 0,
        'min_alignment_score': min(alignment_scores) if alignment_scores else 0,
        'avg_mutations_per_seq': round(sum(mutation_counts) / len(mutation_counts), 2) if mutation_counts else 0,
        'avg_length': sum([r['length'] for r in results]) // len(results) if results else 0,
        # 'high_score_sequences': len([s for s in alignment_scores if s > 1300])
    }

    # Generate mutation analysis
    mutation_analysis = generate_mutation_analysis(results)

    # Store individual sequence details for clickable links
    for result in results:
        sequence_details_store[result['id']] = {
            'id': result['id'],
            'length': result['length'],
            'avg_quality': result['avg_quality'],
            'min_quality': result['min_quality'],
            'max_quality': result['max_quality'],
            'alignment_score': result['alignment_score'],
            'identity_percent': result['identity_percent'],
            'is_reverse_complement': result['is_reverse_complement'],
            'num_mutations': result['num_mutations'],
            'mutations': result['mutations'],
            'insertions': result['insertions'],
            'deletions': result['deletions'],
            'insertion': result['insertion'],
            'deletion': result['deletion'],
            'alignment': result['alignment'],
            'alignment_data': result['alignment_data'],
            'full_sequence': result['full_sequence'],
            'gc_content': result['gc_content'],
            'quality_scores': result['quality_scores'],
            'stored_at': datetime.now().isoformat()
        }

    return {
        'summary': summary,
        'sequences': results,
        'mutation_analysis': mutation_analysis,
        'parameters': {
            'match_score': match_score,
            'mismatch_score': mismatch_score,
            'open_gap_score': open_gap_score,
            'extend_gap_score': extend_gap_score
        }
    }

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main page with all functionalities"""
    with open("static/index.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/fastq-alignment", response_class=HTMLResponse)
async def fastq_alignment_page():
    """Serve the FASTQ alignment page"""
    with open("static/fastq-alignment.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/sequence-alignment", response_class=HTMLResponse)
async def sequence_alignment_page():
    """Serve the two sequence alignment page"""
    with open("static/sequence-alignment.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/sequence-details", response_class=HTMLResponse)
async def sequence_details_page():
    """Serve the sequence details page"""
    with open("static/sequence-details.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/blast-search", response_class=HTMLResponse)
async def blast_search_page():
    """Serve the BLAST search page"""
    with open("static/blast-search.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/blast-job-details", response_class=HTMLResponse)
async def blast_job_details_page():
    """Serve the BLAST job details page"""
    with open("static/blast-job-details.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/fastq-job-details", response_class=HTMLResponse)
async def fastp_job_details_page():
    """Serve the FASTQ job details page"""
    with open("static/fastq-job-details.html", "r") as f:
        return HTMLResponse(content=f.read())


@app.post("/align")
async def align_sequences(
    fastq_file: UploadFile = File(...),
    reference_sequence: str = Form(...),
    job_name: str = Form(""),
    match_score: float = Form(1.0),
    mismatch_score: float = Form(-1.0),
    open_gap_score: float = Form(-2.0),
    extend_gap_score: float = Form(-0.5)
):
    """
    Submit FASTQ alignment job for processing
    """
    try:
        # Validate file type
        if not fastq_file.filename.endswith(('.fastq', '.fq')):
            raise HTTPException(status_code=400, detail="Please upload a FASTQ file (.fastq or .fq)")

        # Read file content
        content = await fastq_file.read()
        fastq_content = content.decode('utf-8')

        # Validate reference sequence
        if not reference_sequence.strip():
            raise HTTPException(status_code=400, detail="Reference sequence cannot be empty")

        # Clean reference sequence (remove whitespace and convert to uppercase)
        reference_sequence = reference_sequence.strip().upper().replace(' ', '').replace('\n', '')

        # Generate unique job ID
        job_id = str(uuid.uuid4())

        # Create job metadata
        job_data = {
            "job_id": job_id,
            "job_name": job_name.strip() if job_name.strip() else f"FASTQ Alignment {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            "status": "completed",  # For now, process immediately
            "created_at": datetime.now().isoformat(),
            "completed_at": None,
            "filename": fastq_file.filename,
            "reference_sequence": reference_sequence,
            "reference_length": len(reference_sequence),
            "parameters": {
                "match_score": match_score,
                "mismatch_score": mismatch_score,
                "open_gap_score": open_gap_score,
                "extend_gap_score": extend_gap_score
            }
        }

        # Process alignment with custom parameters
        results = process_fastq_alignment(fastq_content, reference_sequence, match_score, mismatch_score, open_gap_score, extend_gap_score)

        # Update job completion time and sequence count
        job_data["completed_at"] = datetime.now().isoformat()
        job_data["sequence_count"] = len(results.get("sequences", []))

        # Store results in file
        file_path = fastp_storage.save_job_result(job_id, results)
        job_data["result"] = {
            "stored_in_file": True,
            "file_path": file_path,
            "summary": results.get("summary", {})
        }

        # Save job metadata
        fastp_job_storage.save_job(job_id, job_data)

        # Update in-memory database
        fastp_jobs_db[job_id] = job_data

        return JSONResponse(content={
            "job_id": job_id,
            "status": "completed",
            "message": "FASTQ alignment completed successfully",
            "sequence_count": job_data["sequence_count"],
            "job_name": job_data["job_name"]
        })

    except Exception as e:
        logger.error(f"Error processing FASTQ alignment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing alignment: {str(e)}")





@app.get("/sequence/{sequence_id:path}")
async def get_sequence_details(sequence_id: str):
    """Get detailed information for a specific sequence"""
    # Decode URL-encoded sequence ID
    from urllib.parse import unquote
    decoded_sequence_id = unquote(sequence_id)

    # Check if sequence details are available in the store
    if decoded_sequence_id in sequence_details_store:
        sequence_data = sequence_details_store[decoded_sequence_id]
        logger.info(f"Retrieved sequence details for: {decoded_sequence_id}")
        return sequence_data
    else:
        # Sequence not found in store
        logger.warning(f"Sequence details not found for: {decoded_sequence_id}")
        return {
            "error": "Sequence not found",
            "message": f"No details available for sequence '{decoded_sequence_id}'. This sequence may be from an older session or the data may have been cleared.",
            "sequence_id": decoded_sequence_id,
            "suggestion": "Please run a new FASTQ alignment to generate fresh sequence data."
        }

@app.get("/sequence-store/stats")
async def get_sequence_store_stats():
    """Get statistics about the sequence details store"""
    return {
        "total_sequences": len(sequence_details_store),
        "sequence_ids": list(sequence_details_store.keys())[:10],  # Show first 10 IDs
        "message": "This is an in-memory store for demo purposes. In production, use a database."
    }

@app.delete("/sequence-store/clear")
async def clear_sequence_store():
    """Clear the sequence details store (for testing)"""
    global sequence_details_store
    count = len(sequence_details_store)
    sequence_details_store.clear()
    return {
        "message": f"Cleared {count} sequences from the store",
        "remaining_sequences": len(sequence_details_store)
    }

@app.post("/align-two-sequences")
async def align_two_sequences(
    sequence1: str = Form(...),
    sequence2: str = Form(...),
    match_score: float = Form(1.0),
    mismatch_score: float = Form(-1.0),
    open_gap_score: float = Form(-2.0),
    extend_gap_score: float = Form(-0.5)
):
    """
    Align two sequences with configurable parameters
    """
    try:
        # Validate sequences
        if not sequence1.strip() or not sequence2.strip():
            raise HTTPException(status_code=400, detail="Both sequences must be provided")

        # Clean sequences (remove whitespace and convert to uppercase)
        seq1 = sequence1.strip().upper().replace(' ', '').replace('\n', '')
        seq2 = sequence2.strip().upper().replace(' ', '').replace('\n', '')

        # Validate sequence characters (basic DNA/RNA/protein validation)
        valid_chars = set('ACGTUNRYSWKMBDHV-')  # DNA, RNA, and ambiguous nucleotides
        if not all(c in valid_chars for c in seq1) or not all(c in valid_chars for c in seq2):
            # If not valid nucleotides, check for protein
            protein_chars = set('ACDEFGHIKLMNPQRSTVWY*-')
            if not all(c in protein_chars for c in seq1) or not all(c in protein_chars for c in seq2):
                raise HTTPException(status_code=400, detail="Sequences contain invalid characters")

        # Setup aligner with custom parameters
        aligner = Align.PairwiseAligner()
        aligner.mode = 'global'
        aligner.match_score = match_score
        aligner.mismatch_score = mismatch_score
        aligner.open_gap_score = open_gap_score
        aligner.extend_gap_score = extend_gap_score

        # Get mutations and alignment data
        mutations, alignment_data, has_insertion, has_deletion = get_mutation_positions_and_alignment(seq1, seq2, aligner)

        # Calculate alignment score
        alignment = aligner.align(seq1, seq2)[0]
        alignment_score = alignment.score

        # Prepare result
        result = {
            'sequence1': seq1,
            'sequence2': seq2,
            'alignment_score': alignment_score,
            'num_mutations': len(mutations),
            'mutations': mutations,
            'alignment_data': alignment_data,
            'parameters': {
                'match_score': match_score,
                'mismatch_score': mismatch_score,
                'open_gap_score': open_gap_score,
                'extend_gap_score': extend_gap_score
            }
        }

        return JSONResponse(content=result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing alignment: {str(e)}")

@app.post("/align-multiple-sequences")
async def align_multiple_sequences(
    fasta_sequences: str = Form(...),
    max_iterations: int = Form(16),
    gap_penalty: float = Form(-12.0)
):
    """
    Perform multiple sequence alignment using MUSCLE
    """
    try:
        if not MUSCLE_AVAILABLE:
            raise HTTPException(status_code=503, detail="MUSCLE is not available on this server")

        # Parse FASTA sequences
        sequences = []
        sequence_names = []

        # Split by lines and process FASTA format
        lines = fasta_sequences.strip().split('\n')
        current_seq = ""
        current_name = ""

        for line in lines:
            line = line.strip()
            if line.startswith('>'):
                # Save previous sequence if exists
                if current_name and current_seq:
                    sequences.append(current_seq)
                    sequence_names.append(current_name)
                # Start new sequence
                current_name = line[1:].strip() or f"Sequence_{len(sequence_names) + 1}"
                current_seq = ""
            elif line:
                current_seq += line.upper()

        # Add the last sequence
        if current_name and current_seq:
            sequences.append(current_seq)
            sequence_names.append(current_name)

        if len(sequences) < 2:
            raise HTTPException(status_code=400, detail="At least 2 sequences are required for multiple sequence alignment")

        if len(sequences) > 50:
            raise HTTPException(status_code=400, detail="Maximum 50 sequences allowed")

        # Create temporary input file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fasta', delete=False) as input_file:
            for i, (name, seq) in enumerate(zip(sequence_names, sequences)):
                input_file.write(f">{name}\n{seq}\n")
            input_file_path = input_file.name

        # Create temporary output file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fasta', delete=False) as output_file:
            output_file_path = output_file.name

        try:
            # Run MUSCLE alignment
            muscle_cmd = [
                MUSCLE_PATH,
                '-in', input_file_path,
                '-out', output_file_path,
                '-maxiters', str(max_iterations)
            ]

            logger.info(f"Running MUSCLE command: {' '.join(muscle_cmd)}")

            result = subprocess.run(
                muscle_cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )

            if result.returncode != 0:
                logger.error(f"MUSCLE failed: {result.stderr}")
                raise HTTPException(status_code=500, detail=f"MUSCLE alignment failed: {result.stderr}")

            # Read aligned sequences
            aligned_sequences = []
            aligned_names = []

            with open(output_file_path, 'r') as f:
                content = f.read()

            # Parse aligned FASTA output
            lines = content.strip().split('\n')
            current_seq = ""
            current_name = ""

            for line in lines:
                line = line.strip()
                if line.startswith('>'):
                    if current_name and current_seq:
                        aligned_sequences.append(current_seq)
                        aligned_names.append(current_name)
                    current_name = line[1:].strip()
                    current_seq = ""
                elif line:
                    current_seq += line

            if current_name and current_seq:
                aligned_sequences.append(current_seq)
                aligned_names.append(current_name)

            # Calculate conservation scores
            conservation_scores = calculate_conservation(aligned_sequences)

            # Format alignment for display
            formatted_alignment = format_msa_display(aligned_names, aligned_sequences)

            result_data = {
                'success': True,
                'num_sequences': len(aligned_sequences),
                'alignment_length': len(aligned_sequences[0]) if aligned_sequences else 0,
                'sequences': aligned_sequences,
                'sequence_names': aligned_names,
                'conservation_scores': conservation_scores,
                'formatted_alignment': formatted_alignment,
                'parameters': {
                    'max_iterations': max_iterations,
                    'gap_penalty': gap_penalty
                }
            }

            return JSONResponse(content=result_data)

        finally:
            # Clean up temporary files
            try:
                os.unlink(input_file_path)
                os.unlink(output_file_path)
            except:
                pass

    except subprocess.TimeoutExpired:
        raise HTTPException(status_code=408, detail="MUSCLE alignment timed out. Try with fewer sequences or reduce max iterations.")
    except Exception as e:
        logger.error(f"Multiple sequence alignment error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing multiple sequence alignment: {str(e)}")

def calculate_conservation(sequences):
    """Calculate conservation scores for each position in the alignment"""
    if not sequences:
        return []

    alignment_length = len(sequences[0])
    conservation_scores = []

    for pos in range(alignment_length):
        # Get all characters at this position
        chars = [seq[pos] for seq in sequences if pos < len(seq)]

        # Skip gap-only positions
        non_gap_chars = [c for c in chars if c != '-']
        if not non_gap_chars:
            conservation_scores.append(0.0)
            continue

        # Calculate conservation as frequency of most common character
        char_counts = {}
        for char in non_gap_chars:
            char_counts[char] = char_counts.get(char, 0) + 1

        max_count = max(char_counts.values())
        conservation = max_count / len(non_gap_chars)
        conservation_scores.append(conservation)

    return conservation_scores

def format_msa_display(names, sequences):
    """Format multiple sequence alignment for display"""
    if not sequences:
        return ""

    # Find maximum name length for alignment
    max_name_length = max(len(name) for name in names) if names else 10

    # Format sequences in blocks of 60 characters
    block_size = 80
    alignment_length = len(sequences[0])
    formatted_blocks = []

    for start in range(0, alignment_length, block_size):
        end = min(start + block_size, alignment_length)
        block_lines = []

        # Add position indicator (starting position only)
        position_indicator = f"Position: {start + 1}"
        block_lines.append(f"{''.ljust(max_name_length + 2)}{position_indicator}")
        block_lines.append("")  # Empty line

        # Add sequences
        for name, seq in zip(names, sequences):
            seq_segment = seq[start:end]
            formatted_name = name[:max_name_length].ljust(max_name_length)
            block_lines.append(f"{formatted_name}  {seq_segment}")

        formatted_blocks.append("\n".join(block_lines))

    return "\n\n".join(formatted_blocks)

@app.post("/submit-blast")
async def submit_blast_job(query: BlastQuery):
    """Submit a BLAST search job"""

    if not query.sequence.strip():
        raise HTTPException(status_code=400, detail="Sequence cannot be empty")

    # Validate parameters if provided
    if query.parameters:
        if query.parameters.evalue < 0:
            raise HTTPException(status_code=400, detail="E-value must be non-negative")
        if query.parameters.max_target_seqs < 1 or query.parameters.max_target_seqs > 5000:
            raise HTTPException(status_code=400, detail="Max target sequences must be between 1 and 5000")
        if query.parameters.word_size not in [2, 3, 4, 5, 6]:
            raise HTTPException(status_code=400, detail="Word size must be between 2 and 6")
        if query.parameters.matrix not in ["BLOSUM45", "BLOSUM62", "BLOSUM80", "PAM30", "PAM70", "PAM250"]:
            raise HTTPException(status_code=400, detail="Invalid scoring matrix")
        if query.parameters.gap_open < 1 or query.parameters.gap_open > 50:
            raise HTTPException(status_code=400, detail="Gap open penalty must be between 1 and 50")
        if query.parameters.gap_extend < 1 or query.parameters.gap_extend > 10:
            raise HTTPException(status_code=400, detail="Gap extend penalty must be between 1 and 10")

    # Generate unique job ID
    job_id = str(uuid.uuid4())

    # Use default parameters if none provided
    parameters = query.parameters or BlastParameters()

    # Create job record
    job_record = {
        "job_id": job_id,
        "status": "pending",
        "created_at": datetime.now(),
        "completed_at": None,
        "sequence": query.sequence,
        "sequence_length": len(query.sequence.replace('\n', '').replace('\r', '').replace(' ', '')),
        "job_name": query.job_name or f"BLAST Job {job_id[:8]}",
        # "email": query.email,
        "parameters": {
            "evalue": parameters.evalue,
            "max_target_seqs": parameters.max_target_seqs,
            "word_size": parameters.word_size,
            "matrix": parameters.matrix,
            "gap_open": parameters.gap_open,
            "gap_extend": parameters.gap_extend,
            "database": parameters.database
        },
        "result": None,
        "error": None
    }

    # Save to both in-memory cache and persistent storage
    blast_jobs_db[job_id] = job_record
    blast_job_storage.save_job(job_id, job_record)

    # Submit to Celery queue
    print(f"DEBUG: Submitting BLAST job {job_id} to Celery queue with parameters: {job_record['parameters']}")
    try:
        task = run_blast_search.delay(job_id, query.sequence, job_record['parameters'])
        print(f"DEBUG: Celery task created with ID: {task.id}")

        # Store Celery task ID in both places
        blast_jobs_db[job_id]["celery_task_id"] = task.id
        blast_job_storage.update_job_status(job_id, "pending", celery_task_id=task.id)
        logger.info(f"Job {job_id} status set to 'pending' with Celery task ID: {task.id}")
    except Exception as e:
        print(f"DEBUG: Celery submission failed: {e}")
        
        blast_jobs_db[job_id].update({
            'status': 'failed',
            'completed_at': datetime.now(),
            'error': f"execution failed: {str(e)}"
        })


    return {
        "job_id": job_id,
        "status": "pending",
        "message": "BLAST job submitted successfully",
        "parameters": job_record['parameters']
    }

@app.get("/blast-job/{job_id}")
async def get_job_status(job_id: str):
    """Get the status and results of a BLAST job"""

    if job_id not in blast_jobs_db:
        raise HTTPException(status_code=404, detail="Job not found")

    job = blast_jobs_db[job_id]

    # Check Celery task status if we have a task ID and job is not already in a final state
    if "celery_task_id" in job and job["status"] not in ["completed", "failed"]:
        celery_task = celery_app.AsyncResult(job["celery_task_id"])
        print(f"DEBUG: Checking Celery task {job['celery_task_id']}, state: {celery_task.state}")

        if celery_task.state == "PENDING":
            if job["status"] != "pending":
                job["status"] = "pending"
                blast_job_storage.update_job_status(job_id, "pending")
        elif celery_task.state == "STARTED":
            if job["status"] != "running":
                job["status"] = "running"
                blast_job_storage.update_job_status(job_id, "running")
                logger.info(f"Job {job_id} status updated to running (STARTED)")
        elif celery_task.state == "PROGRESS":
            if job["status"] != "running":
                job["status"] = "running"
                blast_job_storage.update_job_status(job_id, "running")
                logger.info(f"Job {job_id} status updated to running (PROGRESS)")
        elif celery_task.state == "SUCCESS":
            # Handle file-based storage results
            task_result = celery_task.result

            # Check if the task actually failed but returned a result with error status
            if task_result and isinstance(task_result, dict) and task_result.get("status") == "failed":
                job["status"] = "failed"
                job["completed_at"] = datetime.now()
                job["error"] = task_result.get("error", "Unknown error occurred during BLAST search")
                job["result"] = None
                print(f"DEBUG: Task returned failure status: {job['error']}")

                # Save to persistent storage
                blast_job_storage.update_job_status(
                    job_id, "failed",
                    completed_at=job["completed_at"].isoformat(),
                    error=job["error"],
                    result=None
                )
            else:
                job["status"] = "completed"
                job["completed_at"] = datetime.now()

                if task_result and "result_file_path" in task_result:
                    # Results are stored in file
                    job["result_file_path"] = task_result["result_file_path"]
                    job["result_summary"] = task_result.get("result_summary")
                    job["result"] = {
                        "stored_in_file": True,
                        "file_path": task_result["result_file_path"],
                        "summary": task_result.get("result_summary", {})
                    }
                    print(f"DEBUG: Task completed, results stored in file: {task_result['result_file_path']}")

                    # Save to persistent storage
                    blast_job_storage.update_job_status(
                        job_id, "completed",
                        completed_at=job["completed_at"].isoformat(),
                        result_file_path=job["result_file_path"],
                        result_summary=job["result_summary"],
                        result=job["result"]
                    )
                else:
                    # Fallback: results in memory (backward compatibility)
                    job["result"] = task_result
                    print(f"DEBUG: Task completed, results in memory: {list(task_result.keys()) if task_result else 'None'}")

                    # Save to persistent storage
                    blast_job_storage.update_job_status(
                        job_id, "completed",
                        completed_at=job["completed_at"].isoformat(),
                        result=task_result
                    )
        elif celery_task.state == "FAILURE":
            job["status"] = "failed"
            job["completed_at"] = datetime.now()
            job["error"] = str(celery_task.info)
            job["result"] = None  # Clear any previous result data
            print(f"DEBUG: Task failed with error: {celery_task.info}")

            # Save to persistent storage
            blast_job_storage.update_job_status(
                job_id, "failed",
                completed_at=job["completed_at"].isoformat(),
                error=job["error"],
                result=None
            )
        elif celery_task.state == "RETRY":
            if job["status"] != "running":
                job["status"] = "running"  # Show as running during retries
                blast_job_storage.update_job_status(job_id, "running")
            print(f"DEBUG: Task is retrying")
        elif celery_task.state == "REVOKED":
            job["status"] = "failed"
            job["completed_at"] = datetime.now()
            job["error"] = "Task was cancelled or revoked"
            job["result"] = None
            print(f"DEBUG: Task was revoked")

            # Save to persistent storage
            blast_job_storage.update_job_status(
                job_id, "failed",
                completed_at=job["completed_at"].isoformat(),
                error=job["error"],
                result=None
            )
        else:
            print(f"DEBUG: Unknown task state: {celery_task.state}")

    # Also check if we have a task ID but the job status is inconsistent
    elif "celery_task_id" in job and job["status"] in ["completed", "failed"]:
        # Double-check the actual Celery task state for consistency
        celery_task = celery_app.AsyncResult(job["celery_task_id"])
        if celery_task.state == "FAILURE" and job["status"] != "failed":
            print(f"DEBUG: Correcting job status from {job['status']} to failed based on Celery state")
            job["status"] = "failed"
            job["completed_at"] = datetime.now()
            job["error"] = str(celery_task.info)
            job["result"] = None

    return {
        "job_id": job["job_id"],
        "status": job["status"],
        "created_at": job["created_at"],
        "completed_at": job["completed_at"],
        "job_name": job["job_name"],
        "sequence": job["sequence"],
        "sequence_length": job["sequence_length"],
        # "email": job["email"],
        "parameters": job.get("parameters", {}),
        "result": job["result"],
        "error": job["error"]
    }

@app.get("/blast-job/{job_id}/results")
async def get_job_results(job_id: str):
    """Get the full results of a BLAST job from file storage"""

    if job_id not in blast_jobs_db:
        raise HTTPException(status_code=404, detail="Job not found")

    job = blast_jobs_db[job_id]

    if job["status"] != "completed":
        raise HTTPException(status_code=400, detail="Job not completed yet")

    # Try to load results from file storage
    try:
        full_results = blast_storage.load_job_result(job_id)
        if full_results is None:
            # Fallback to in-memory results if file not found
            if job.get("result") and not job["result"].get("stored_in_file"):
                return job["result"]
            else:
                raise HTTPException(status_code=404, detail="Results not found in file storage")

        return {
            "job_id": job_id,
            "job_name": job["job_name"],
            "status": job["status"],
            "completed_at": job["completed_at"],
            "parameters": job.get("parameters", {}),
            "results": full_results
        }

    except Exception as e:
        print(f"DEBUG: Error loading results from file: {e}")
        raise HTTPException(status_code=500, detail=f"Error loading results: {str(e)}")

@app.get("/blast-jobs")
async def list_blast_jobs():
    """List all BLAST jobs"""
    jobs_list = []

    for job in blast_jobs_db.values():
        job_info = {
            "job_id": job["job_id"],
            "status": job["status"],
            "created_at": job["created_at"],
            "completed_at": job["completed_at"],
            "job_name": job["job_name"]
        }

        # Add storage info for completed jobs
        if job["status"] == "completed":
            result_summary = blast_storage.get_result_summary(job["job_id"])
            if result_summary:
                job_info["result_summary"] = result_summary

        jobs_list.append(job_info)

    return jobs_list

@app.get("/blast-job/{job_id}/download")
async def download_blast_results(job_id: str, format: str = "json"):
    """Download BLAST results in various formats (json, csv, tsv, xml)"""

    if job_id not in blast_jobs_db:
        raise HTTPException(status_code=404, detail="Job not found")

    job = blast_jobs_db[job_id]

    if job["status"] != "completed":
        raise HTTPException(status_code=400, detail="Job not completed yet")

    # Load results from file storage or fallback to memory
    try:
        results = blast_storage.load_job_result(job_id)
        if results is None:
            # Fallback to in-memory results
            job_result = job.get("result", {})
            if job_result.get("stored_in_file"):
                raise HTTPException(status_code=404, detail="Results file not found")
            results = job_result.get("results", {})
    except Exception as e:
        print(f"DEBUG: Error loading results for download: {e}")
        raise HTTPException(status_code=500, detail=f"Error loading results: {str(e)}")

    job_name = job.get("job_name", "blast_results")

    # Sanitize filename
    safe_filename = "".join(c for c in job_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    if not safe_filename:
        safe_filename = f"blast_job_{job_id}"

    if format.lower() == "json":
        # JSON format - complete results
        content = json.dumps({
            "job_info": {
                "job_id": job_id,
                "job_name": job["job_name"],
                "sequence": job["sequence"],
                "sequence_length": job["sequence_length"],
                "created_at": job["created_at"],
                "completed_at": job["completed_at"],
                "parameters": job.get("parameters", {})
            },
            "results": results
        }, indent=2, default=str)

        return StreamingResponse(
            io.StringIO(content),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={safe_filename}.json"}
        )

    elif format.lower() in ["csv", "tsv"]:
        # CSV/TSV format - tabular hit data
        delimiter = "," if format.lower() == "csv" else "\t"
        output = io.StringIO()
        writer = csv.writer(output, delimiter=delimiter)

        # Write header
        writer.writerow([
            "Hit_Number", "Hit_ID", "Hit_Description", "Hit_Length",
            "HSP_Number", "E_value", "Bit_Score", "Score", "Identity",
            "Positive", "Gaps", "Align_Length", "Query_Start", "Query_End",
            "Hit_Start", "Hit_End", "Query_Frame", "Hit_Frame",
            "Query_Sequence", "Hit_Sequence", "Midline"
        ])

        # Write data
        if results.get("hits"):
            for hit_idx, hit in enumerate(results["hits"], 1):
                for hsp_idx, hsp in enumerate(hit.get("hsps", []), 1):
                    writer.writerow([
                        hit_idx,
                        hit.get("id", ""),
                        hit.get("description", ""),
                        hit.get("length", ""),
                        hsp_idx,
                        hsp.get("evalue", ""),
                        hsp.get("bit_score", ""),
                        hsp.get("score", ""),
                        hsp.get("identity", ""),
                        hsp.get("positive", ""),
                        hsp.get("gaps", ""),
                        hsp.get("align_len", ""),
                        hsp.get("query_start", ""),
                        hsp.get("query_end", ""),
                        hsp.get("hit_start", ""),
                        hsp.get("hit_end", ""),
                        hsp.get("query_frame", ""),
                        hsp.get("hit_frame", ""),
                        hsp.get("qseq", ""),
                        hsp.get("hseq", ""),
                        hsp.get("midline", "")
                    ])

        content = output.getvalue()
        file_ext = "csv" if format.lower() == "csv" else "tsv"
        media_type = "text/csv" if format.lower() == "csv" else "text/tab-separated-values"

        return StreamingResponse(
            io.StringIO(content),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={safe_filename}.{file_ext}"}
        )

    elif format.lower() == "xml":
        # XML format - structured results
        from xml.etree.ElementTree import Element, SubElement, tostring
        from xml.dom import minidom

        root = Element("blast_results")

        # Job info
        job_info = SubElement(root, "job_info")
        SubElement(job_info, "job_id").text = job_id
        SubElement(job_info, "job_name").text = job.get("job_name", "")
        SubElement(job_info, "sequence_length").text = str(job.get("sequence_length", ""))
        SubElement(job_info, "created_at").text = str(job.get("created_at", ""))
        SubElement(job_info, "completed_at").text = str(job.get("completed_at", ""))

        # Parameters
        params = SubElement(job_info, "parameters")
        for key, value in job.get("parameters", {}).items():
            param = SubElement(params, "parameter")
            param.set("name", key)
            param.text = str(value)

        # Results
        hits_elem = SubElement(root, "hits")
        if results.get("hits"):
            for hit in results["hits"]:
                hit_elem = SubElement(hits_elem, "hit")
                SubElement(hit_elem, "id").text = hit.get("id", "")
                SubElement(hit_elem, "description").text = hit.get("description", "")
                SubElement(hit_elem, "length").text = str(hit.get("length", ""))

                hsps_elem = SubElement(hit_elem, "hsps")
                for hsp in hit.get("hsps", []):
                    hsp_elem = SubElement(hsps_elem, "hsp")
                    for key, value in hsp.items():
                        SubElement(hsp_elem, key).text = str(value)

        # Pretty print XML
        rough_string = tostring(root, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        content = reparsed.toprettyxml(indent="  ")

        return StreamingResponse(
            io.StringIO(content),
            media_type="application/xml",
            headers={"Content-Disposition": f"attachment; filename={safe_filename}.xml"}
        )

    else:
        raise HTTPException(status_code=400, detail="Unsupported format. Use: json, csv, tsv, or xml")

@app.get("/storage/stats")
async def get_storage_stats():
    """Get BLAST results and metadata storage statistics"""
    try:
        results_stats = blast_storage.get_storage_stats()
        metadata_stats = blast_job_storage.get_storage_stats()

        return {
            "status": "success",
            "results_storage": results_stats,
            "metadata_storage": metadata_stats,
            "total_jobs": metadata_stats.get("total_jobs", 0)
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

@app.post("/storage/cleanup")
async def cleanup_old_results(days_old: int = 7):
    """Clean up BLAST result files and metadata older than specified days"""
    try:
        # Clean up result files
        deleted_files = blast_storage.cleanup_old_results(days_old)

        # Clean up job metadata
        deleted_metadata = blast_job_storage.cleanup_old_jobs(days_old)

        # Reload jobs from persistent storage to update in-memory cache
        global blast_jobs_db
        blast_jobs_db = blast_job_storage.load_all_jobs()

        return {
            "status": "success",
            "message": f"Cleaned up {deleted_files} result files and {deleted_metadata} metadata entries older than {days_old} days",
            "deleted_files": deleted_files,
            "deleted_metadata": deleted_metadata
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

@app.delete("/blast-job/{job_id}/results")
async def delete_job_results(job_id: str):
    """Delete BLAST job results from file storage"""

    if job_id not in blast_jobs_db:
        raise HTTPException(status_code=404, detail="Job not found")

    try:
        deleted = blast_storage.delete_job_result(job_id)
        if deleted:
            # Update job record to remove file references
            job = blast_jobs_db[job_id]
            if "result_file_path" in job:
                del job["result_file_path"]
            if "result_summary" in job:
                del job["result_summary"]

            return {
                "status": "success",
                "message": f"Results for job {job_id} deleted successfully"
            }
        else:
            return {
                "status": "warning",
                "message": f"No result file found for job {job_id}"
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting results: {str(e)}")

# FASTQ Job Management Endpoints

@app.get("/fastq-jobs")
async def list_fastp_jobs():
    """List all FASTQ alignment jobs"""
    jobs_list = []

    for job in fastp_jobs_db.values():
        job_info = {
            "job_id": job["job_id"],
            "status": job["status"],
            "created_at": job["created_at"],
            "completed_at": job["completed_at"],
            "job_name": job["job_name"],
            "filename": job.get("filename", ""),
            "sequence_count": job.get("sequence_count", 0),
            "reference_length": job.get("reference_length", 0)
        }

        # Add result summary for completed jobs
        if job["status"] == "completed" and job.get("result", {}).get("summary"):
            job_info["result_summary"] = job["result"]["summary"]

        jobs_list.append(job_info)

    # Sort by creation date (newest first)
    jobs_list.sort(key=lambda x: x["created_at"], reverse=True)

    return jobs_list

@app.get("/fastq-job/{job_id}")
async def get_fastp_job(job_id: str):
    """Get specific FASTQ job details"""
    if job_id not in fastp_jobs_db:
        raise HTTPException(status_code=404, detail="FASTQ job not found")

    job = fastp_jobs_db[job_id]

    # Return job metadata (results are loaded separately)
    return {
        "job_id": job["job_id"],
        "job_name": job["job_name"],
        "status": job["status"],
        "created_at": job["created_at"],
        "completed_at": job["completed_at"],
        "filename": job.get("filename", ""),
        "reference_sequence": job.get("reference_sequence", ""),
        "reference_length": job.get("reference_length", 0),
        "sequence_count": job.get("sequence_count", 0),
        "parameters": job.get("parameters", {}),
        "result": job.get("result", {})
    }

@app.get("/fastq-job/{job_id}/results")
async def get_fastp_job_results(job_id: str):
    """Get full FASTQ job results from file storage"""
    if job_id not in fastp_jobs_db:
        raise HTTPException(status_code=404, detail="FASTQ job not found")

    job = fastp_jobs_db[job_id]

    # Try to load results from file storage
    try:
        full_results = fastp_storage.load_job_result(job_id)
        if full_results is None:
            # Fallback to in-memory results if file not found
            if job.get("result") and not job["result"].get("stored_in_file"):
                return job["result"]
            else:
                raise HTTPException(status_code=404, detail="Results not found in file storage")

        return {
            "job_id": job_id,
            "job_name": job["job_name"],
            "status": job["status"],
            "completed_at": job["completed_at"],
            "parameters": job.get("parameters", {}),
            "results": full_results
        }

    except Exception as e:
        logger.error(f"Error loading FASTQ results from file: {e}")
        raise HTTPException(status_code=500, detail=f"Error loading results: {str(e)}")

@app.get("/fastq-job/{job_id}/download")
async def download_fastp_results(job_id: str, format: str = "json"):
    """Download FASTQ job results in specified format"""
    if job_id not in fastp_jobs_db:
        raise HTTPException(status_code=404, detail="FASTQ job not found")

    job = fastp_jobs_db[job_id]

    # Load full results
    try:
        results = fastp_storage.load_job_result(job_id)
        if results is None:
            raise HTTPException(status_code=404, detail="Results not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading results: {str(e)}")

    # Create safe filename
    safe_filename = f"fastp_job_{job_id[:8]}_{job['job_name'].replace(' ', '_')}"

    if format.lower() == "json":
        # JSON format
        content = json.dumps({
            "job_info": {
                "job_id": job_id,
                "job_name": job["job_name"],
                "created_at": job["created_at"],
                "completed_at": job["completed_at"],
                "filename": job.get("filename", ""),
                "parameters": job.get("parameters", {})
            },
            "results": results
        }, indent=2, default=str)

        return StreamingResponse(
            io.StringIO(content),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={safe_filename}.json"}
        )

    elif format.lower() == "csv":
        # CSV format for sequences
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            "Sequence_ID", "Length", "Avg_Quality", "Min_Quality", "Max_Quality",
            "Alignment_Score", "Identity_Percent", "Mutations", "Insertions", "Deletions"
        ])

        # Write sequence data
        for seq in results.get("sequences", []):
            writer.writerow([
                seq.get("id", ""),
                seq.get("length", ""),
                seq.get("avg_quality", ""),
                seq.get("min_quality", ""),
                seq.get("max_quality", ""),
                seq.get("alignment_score", ""),
                seq.get("identity_percent", ""),
                len(seq.get("mutations", [])),
                len(seq.get("insertions", [])),
                len(seq.get("deletions", []))
            ])

        content = output.getvalue()
        return StreamingResponse(
            io.StringIO(content),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={safe_filename}.csv"}
        )

    else:
        raise HTTPException(status_code=400, detail="Unsupported format. Use: json or csv")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5001)
