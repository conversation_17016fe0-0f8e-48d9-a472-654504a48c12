#!/bin/bash

# Sequence Alignment App with BLAST Celery Queue Startup Script

echo "Starting Sequence Alignment App with BLAST support..."

# Check if Redis is running on port 6380
if ! lsof -i :6380 > /dev/null 2>&1; then
    echo "Starting Redis server on port 6380..."
    redis-server --daemonize yes --port 6380
    sleep 2
else
    echo "Redis server already running on port 6380"
fi

export BINDIR="../../bin"  # Or wherever blastp is installed locally

echo "Starting services..."

# Start FastAPI backend
echo "Starting FastAPI backend on port 5001..."
uvicorn main:app --host 0.0.0.0 --port 5001 --reload --log-level info &
BACKEND_PID=$!

# # Start Celery worker for BLAST tasks (in foreground to see debug output)
# echo "Starting Celery worker for BLAST tasks..."
# echo "Debug output will appear below:"
# echo "================================"
# celery -A celery_app worker --loglevel=debug

# echo ""
# echo "✅ Services started successfully!"
# echo ""
# echo "🌐 Sequence Alignment App: http://localhost:5001"
# echo "📚 API Documentation: http://localhost:5001/docs"
# echo "🔬 BLAST Search: http://localhost:5001/blast-search"
# echo ""
# echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap "echo 'Stopping services...'; kill $BACKEND_PID 2>/dev/null; exit" INT
wait
