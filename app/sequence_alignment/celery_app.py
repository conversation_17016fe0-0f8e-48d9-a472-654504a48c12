from celery import Celery
import os

# Redis configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6380/0")

# Create Celery app
celery_app = Celery(
    "sequence_alignment_worker",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=["blast_tasks"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=80 * 60,  # 90 minutes
    task_soft_time_limit=75 * 60,  # 85 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

if __name__ == "__main__":
    celery_app.start()
