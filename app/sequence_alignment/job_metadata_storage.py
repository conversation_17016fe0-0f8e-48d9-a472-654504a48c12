"""
Persistent Job Metadata Storage

This module handles persistent storage of BLAST job metadata using JSON files.
Job metadata includes job info, status, parameters, etc. but not the large result data.
"""

import os
import json
import fcntl
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class JobMetadataStorage:
    """Manages persistent storage for BLAST job metadata"""
    
    def __init__(self, base_dir: Optional[str] = None):
        """
        Initialize the job metadata storage
        
        Args:
            base_dir: Base directory for storing metadata. If None, uses environment variable
                     BLAST_RESULTS_DIR or defaults to './data/blast_results'
        """
        if base_dir is None:
            base_dir = os.getenv("BLAST_RESULTS_DIR", "./data/blast_results")
        
        self.base_dir = Path(base_dir)
        self.metadata_dir = self.base_dir / "metadata"
        self.jobs_file = self.metadata_dir / "jobs.json"
        
        # Create directories if they don't exist
        self.metadata_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize jobs file if it doesn't exist
        if not self.jobs_file.exists():
            self._save_jobs({})
        
        logger.info(f"JobMetadataStorage initialized with metadata file: {self.jobs_file}")
    
    def _load_jobs(self) -> Dict[str, Any]:
        """Load all jobs from the JSON file with file locking"""
        try:
            with open(self.jobs_file, 'r', encoding='utf-8') as f:
                # Use file locking to prevent concurrent access issues
                fcntl.flock(f.fileno(), fcntl.LOCK_SH)  # Shared lock for reading
                data = json.load(f)
                fcntl.flock(f.fileno(), fcntl.LOCK_UN)  # Unlock
                return data
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load jobs file: {e}, returning empty dict")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error loading jobs: {e}")
            return {}
    
    def _save_jobs(self, jobs: Dict[str, Any]) -> bool:
        """Save all jobs to the JSON file with file locking"""
        try:
            # Write to temporary file first, then rename for atomic operation
            temp_file = self.jobs_file.with_suffix('.tmp')
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                # Use exclusive lock for writing
                fcntl.flock(f.fileno(), fcntl.LOCK_EX)
                json.dump(jobs, f, indent=2, default=str, ensure_ascii=False)
                fcntl.flock(f.fileno(), fcntl.LOCK_UN)
            
            # Atomic rename
            temp_file.rename(self.jobs_file)
            return True
            
        except Exception as e:
            logger.error(f"Failed to save jobs file: {e}")
            # Clean up temp file if it exists
            if temp_file.exists():
                temp_file.unlink()
            return False
    
    def save_job(self, job_id: str, job_data: Dict[str, Any]) -> bool:
        """Save or update a single job's metadata"""
        try:
            jobs = self._load_jobs()
            
            # Add timestamp for when metadata was last updated
            job_data_copy = job_data.copy()
            job_data_copy['metadata_updated_at'] = datetime.now().isoformat()
            
            jobs[job_id] = job_data_copy
            
            success = self._save_jobs(jobs)
            if success:
                logger.info(f"Saved job metadata for {job_id}")
            else:
                logger.error(f"Failed to save job metadata for {job_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error saving job {job_id}: {e}")
            return False
    
    def load_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Load a single job's metadata"""
        try:
            jobs = self._load_jobs()
            return jobs.get(job_id)
        except Exception as e:
            logger.error(f"Error loading job {job_id}: {e}")
            return None
    
    def load_all_jobs(self) -> Dict[str, Any]:
        """Load all jobs metadata"""
        return self._load_jobs()
    
    def delete_job(self, job_id: str) -> bool:
        """Delete a job's metadata"""
        try:
            jobs = self._load_jobs()
            
            if job_id in jobs:
                del jobs[job_id]
                success = self._save_jobs(jobs)
                if success:
                    logger.info(f"Deleted job metadata for {job_id}")
                return success
            else:
                logger.warning(f"Job {job_id} not found for deletion")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting job {job_id}: {e}")
            return False
    
    def update_job_status(self, job_id: str, status: str, **kwargs) -> bool:
        """Update job status and other fields"""
        try:
            jobs = self._load_jobs()
            
            if job_id in jobs:
                jobs[job_id]['status'] = status
                jobs[job_id]['metadata_updated_at'] = datetime.now().isoformat()
                
                # Update any additional fields
                for key, value in kwargs.items():
                    jobs[job_id][key] = value
                
                success = self._save_jobs(jobs)
                if success:
                    logger.debug(f"Updated job {job_id} status to {status}")
                return success
            else:
                logger.warning(f"Job {job_id} not found for status update")
                return False
                
        except Exception as e:
            logger.error(f"Error updating job {job_id} status: {e}")
            return False
    
    def get_jobs_by_status(self, status: str) -> List[Dict[str, Any]]:
        """Get all jobs with a specific status"""
        try:
            jobs = self._load_jobs()
            return [job for job in jobs.values() if job.get('status') == status]
        except Exception as e:
            logger.error(f"Error getting jobs by status {status}: {e}")
            return []
    
    def cleanup_old_jobs(self, days_old: int = 30) -> int:
        """Remove job metadata older than specified days"""
        try:
            from datetime import timedelta
            
            jobs = self._load_jobs()
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            jobs_to_delete = []
            for job_id, job_data in jobs.items():
                created_at_str = job_data.get('created_at')
                if created_at_str:
                    try:
                        created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                        if created_at < cutoff_date:
                            jobs_to_delete.append(job_id)
                    except ValueError:
                        logger.warning(f"Invalid date format for job {job_id}: {created_at_str}")
            
            # Delete old jobs
            for job_id in jobs_to_delete:
                del jobs[job_id]
            
            if jobs_to_delete:
                success = self._save_jobs(jobs)
                if success:
                    logger.info(f"Cleaned up {len(jobs_to_delete)} old job metadata entries")
                    return len(jobs_to_delete)
            
            return 0
            
        except Exception as e:
            logger.error(f"Error cleaning up old jobs: {e}")
            return 0
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get metadata storage statistics"""
        try:
            jobs = self._load_jobs()
            
            # Count jobs by status
            status_counts = {}
            for job in jobs.values():
                status = job.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # Get file size
            file_size = self.jobs_file.stat().st_size if self.jobs_file.exists() else 0
            
            return {
                "total_jobs": len(jobs),
                "status_counts": status_counts,
                "metadata_file_size_bytes": file_size,
                "metadata_file_size_kb": round(file_size / 1024, 2),
                "metadata_file_path": str(self.jobs_file),
                "file_exists": self.jobs_file.exists()
            }
            
        except Exception as e:
            logger.error(f"Error getting storage stats: {e}")
            return {
                "total_jobs": 0,
                "status_counts": {},
                "metadata_file_size_bytes": 0,
                "metadata_file_size_kb": 0,
                "metadata_file_path": str(self.jobs_file),
                "file_exists": False,
                "error": str(e)
            }


# Global metadata storage instance
# job_metadata_storage = JobMetadataStorage()
