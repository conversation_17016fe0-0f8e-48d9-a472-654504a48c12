from Bio import <PERSON>q<PERSON>

def extract_first_n_fastq(input_file, output_file, n=100):
    """
    Extract the first n records from a FASTQ file and write to a new FASTQ file using Biopython.
    
    Args:
        input_file (str): Path to input FASTQ file
        output_file (str): Path to output FASTQ file
        n (int): Number of records to extract (default: 100)
    """
    try:
        # Read and write the first n records
        records = SeqIO.parse(input_file, "fastq")
        count = SeqIO.write((record for i, record in enumerate(records) if i < n), output_file, "fastq")
        print(f"Successfully wrote {count} records to {output_file}")
    
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found")
    except Exception as e:
        print(f"Error occurred: {str(e)}")

# Example usage
if __name__ == "__main__":
    input_fastq = "/export/kellywang/cases/ngs/P3-atg-B2_TSM20250626-021-00118_20250627-BAN3-0_G01_1751099157082_k3ytn8.fastq"  # Replace with your input FASTQ file path
    output_fastq = "output_100.fastq"  # Output file name
    extract_first_n_fastq(input_fastq, output_fastq, 100)