# Sequence Alignment Tool - Comprehensive Documentation

This document provides complete documentation for the Sequence Alignment Tool, covering BLAST integration, file storage, and persistent job management.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [BLAST Integration](#blast-integration)
4. [File Storage System](#file-storage-system)
5. [Persistent Job Management](#persistent-job-management)
6. [API Reference](#api-reference)
7. [Installation & Setup](#installation--setup)
8. [Testing](#testing)
9. [Production Deployment](#production-deployment)
10. [Troubleshooting](#troubleshooting)

---

## 🎯 Overview

The Sequence Alignment Tool is a comprehensive web application that provides:

- **FASTQ Alignment**: Process FASTQ files against reference sequences
- **BLAST Protein Search**: Search protein sequences against nr database
- **Multiple Sequence Alignment**: MUSCLE-based MSA functionality
- **Job Management**: Persistent job storage with Celery queue processing
- **File Storage**: Efficient file-based storage for large results

### Key Features

- ✅ **Real-time Processing**: Immediate results for FASTQ alignment
- ✅ **Background Jobs**: Celery-based BLAST processing
- ✅ **Persistent Storage**: Jobs survive server restarts
- ✅ **File-based Results**: Efficient storage for large datasets
- ✅ **Web Interface**: User-friendly HTML/JS frontend
- ✅ **API Access**: RESTful API for programmatic access

---

## 🏗️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │───▶│  FastAPI Backend │───▶│  Celery Worker  │
│  (HTML/JS/CSS)  │    │   (main.py)     │    │ (blast_tasks.py)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Job Storage    │    │  BLAST Execution│
                       │ (Persistent)    │    │   (blastp cmd)  │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                └────────────────────────┘
                                      Redis Queue
```

### Storage Architecture

```
data/
├── blast_results/
│   ├── metadata/
│   │   └── jobs.json           # Job metadata
│   └── results/
│       ├── job_uuid_1.json     # BLAST results
│       └── job_uuid_2.json
└── fastp_results/
    ├── metadata/
    │   └── jobs.json           # FASTQ job metadata
    └── results/
        ├── job_uuid_1.json     # FASTQ results
        └── job_uuid_2.json
```

---

## 🧬 BLAST Integration

### Overview

BLAST (Basic Local Alignment Search Tool) integration provides protein sequence search capabilities using a Celery queue for background processing.

### Features

- **Asynchronous Processing**: Jobs run in background via Celery
- **Real-time Status**: Live job status updates
- **Result Storage**: Large results stored in files
- **Job History**: Persistent job tracking

### BLAST Workflow

1. **Job Submission**: User submits protein sequence
2. **Queue Processing**: Job added to Celery queue
3. **BLAST Execution**: Worker runs blastp command
4. **Result Storage**: Results saved to file
5. **Status Update**: Job marked as completed
6. **Result Display**: User views formatted results

### Configuration

```python
# BLAST Parameters
BLAST_PARAMS = {
    'database': 'nr',
    'evalue': 10.0,
    'max_target_seqs': 100,
    'output_format': 'xml'
}
```

---

## 💾 File Storage System

### Overview

The file-based storage system handles large result datasets efficiently by storing data in JSON files while keeping metadata in memory.

### Benefits

- **Persistence**: Results survive server restarts
- **Memory Efficiency**: Large results don't consume server memory
- **Scalability**: Handles large datasets
- **Backup Capability**: Easy backup and transfer

### Storage Components

1. **BlastResultsStorage**: Core storage manager for BLAST results
2. **FastpResultsStorage**: Storage manager for FASTQ results
3. **Job Metadata**: In-memory cache with persistent backup
4. **Result Files**: JSON files on disk

### File Format

```json
{
  "job_id": "uuid-here",
  "saved_at": "2024-07-09T10:30:00",
  "file_version": "1.0",
  "data": {
    "sequences": [...],
    "summary": {...},
    "parameters": {...}
  }
}
```

---

## 🔄 Persistent Job Management

### Overview

The persistent job storage system ensures jobs survive server restarts using a dual approach:
- **In-memory cache**: Fast access for active operations
- **Persistent storage**: JSON file-based storage for durability

### Job Lifecycle

1. **Creation**: Job created in memory and saved to disk
2. **Processing**: Status updates synchronized to storage
3. **Completion**: Final results and status persisted
4. **Cleanup**: Old jobs automatically cleaned up

### Job Metadata Structure

```json
{
  "job_id": "uuid-string",
  "status": "pending|running|completed|failed",
  "created_at": "2024-07-09T10:30:00",
  "completed_at": "2024-07-09T10:35:00",
  "job_name": "User-provided job name",
  "sequence_count": 100,
  "parameters": {...},
  "result": {
    "stored_in_file": true,
    "file_path": "/path/to/result.json",
    "summary": {...}
  }
}
```

### Concurrency Safety

- **File Locking**: Uses `fcntl` for safe concurrent access
- **Atomic Operations**: Temporary files for atomic updates
- **Error Handling**: Graceful fallback mechanisms

---

## 🌐 API Reference

### FASTQ Alignment

```http
POST /align
Content-Type: multipart/form-data

fastq_file: [file]
reference_sequence: "ATCG..."
job_name: "My Alignment"
match_score: 1.0
mismatch_score: -1.0
open_gap_score: -2.0
extend_gap_score: -0.5
```

### BLAST Search

```http
POST /submit-blast
Content-Type: application/json

{
    "sequence": "MKTVRQERLK...",
    "job_name": "My BLAST Search",
}
```

### Job Management

```http
GET /blast-jobs                    # List all jobs
GET /blast-job/{job_id}           # Get job status
GET /blast-job/{job_id}/results   # Get full results
GET /blast-job/{job_id}/download  # Download results
DELETE /blast-job/{job_id}        # Delete job
```

### Storage Management

```http
GET /storage/stats          # Storage statistics
POST /storage/cleanup       # Cleanup old files
```

---

## 🚀 Installation & Setup

### Prerequisites

```bash
# Install system dependencies
sudo apt-get update
sudo apt-get install redis-server ncbi-blast+ muscle

# Install Python dependencies
pip install -r requirements.txt
```

### Quick Start

```bash
# Start all services
./start_with_celery.sh
```

This starts:
- Redis server (port 6379)
- FastAPI backend (port 5001)
- Celery worker for BLAST tasks

### Manual Setup

```bash
# Start Redis
redis-server --daemonize yes --port 6379

# Start FastAPI Backend
python3 main.py

# Start Celery Worker (in separate terminal)
celery -A celery_app worker --loglevel=info
```

### Environment Configuration

```bash
# Storage directories
export BLAST_RESULTS_DIR="/path/to/blast/results"
export FASTP_RESULTS_DIR="/path/to/fastq/results"

# Binary locations
export BINDIR="/app/bin"
```

---

## 🧪 Testing

### Integration Tests

```bash
# Test BLAST integration
python3 test_blast_celery.py

# Test file storage
python3 test_file_storage.py

# Test persistent jobs
python3 test_persistent_jobs.py
```

### Manual Testing

1. **FASTQ Alignment**:
   - Visit http://localhost:5001
   - Upload FASTQ file and reference sequence
   - Verify immediate results display

2. **BLAST Search**:
   - Visit http://localhost:5001/blast-search
   - Submit protein sequence
   - Monitor job status updates

3. **Job Persistence**:
   - Submit jobs, restart server
   - Verify jobs are still listed

---

## 🚀 Production Deployment

### System Requirements

- **CPU**: Multi-core recommended for Celery workers
- **Memory**: 4GB+ for BLAST database operations
- **Storage**: SSD recommended, 100GB+ for results
- **Network**: Stable connection for database downloads

### Configuration

```yaml
# docker-compose.yml example
version: '3.8'
services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
  
  app:
    build: .
    ports:
      - "5001:5001"
    environment:
      - BLAST_RESULTS_DIR=/data/blast_results
      - FASTP_RESULTS_DIR=/data/fastp_results
    volumes:
      - ./data:/data
  
  worker:
    build: .
    command: celery -A celery_app worker --loglevel=info
    environment:
      - BLAST_RESULTS_DIR=/data/blast_results
    volumes:
      - ./data:/data
```

### Monitoring

1. **Storage Statistics**: Monitor via `/storage/stats`
2. **Job Queues**: Monitor Celery queue lengths
3. **Disk Usage**: Set up alerts for storage directories
4. **Log Monitoring**: Track application and worker logs

### Backup Strategy

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d)
tar -czf backup_${DATE}.tar.gz data/
aws s3 cp backup_${DATE}.tar.gz s3://your-backup-bucket/
```

---

## 🐛 Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   ```bash
   # Check Redis status
   redis-cli ping
   
   # Restart Redis
   sudo systemctl restart redis
   ```

2. **Celery Worker Not Starting**
   ```bash
   # Check Celery installation
   celery --version
   
   # Start with debug logging
   celery -A celery_app worker --loglevel=debug
   ```

3. **Storage Permission Errors**
   ```bash
   # Fix permissions
   sudo chown -R $USER:$USER data/
   chmod -R 755 data/
   ```

4. **Job Stuck in Pending**
   - Verify Celery worker is running
   - Check Redis connection
   - Review worker logs for errors

### Recovery Procedures

1. **Corrupted Job Metadata**
   ```bash
   # Backup and validate
   cp data/blast_results/metadata/jobs.json jobs.backup
   python3 -m json.tool jobs.backup
   ```

2. **Missing Result Files**
   - Check file paths in job metadata
   - Restore from backup if available
   - Re-run jobs if necessary

### Performance Optimization

1. **Storage Performance**
   - Use SSD storage for better I/O
   - Separate storage from OS disk
   - Monitor file system performance

2. **Memory Management**
   - Configure appropriate Celery worker memory limits
   - Implement result file cleanup policies
   - Monitor memory usage patterns

3. **Queue Management**
   - Scale Celery workers based on load
   - Implement job prioritization
   - Monitor queue lengths

---

## 📈 Future Enhancements

### Planned Features

1. **Database Integration**: Replace file storage with PostgreSQL
2. **Cloud Storage**: Support for S3/Azure blob storage
3. **User Authentication**: Multi-user support with permissions
4. **Advanced Analytics**: Job statistics and reporting
5. **API Rate Limiting**: Prevent abuse and ensure fair usage
6. **Result Caching**: Intelligent caching for repeated queries

### Scalability Improvements

1. **Distributed Workers**: Multi-server Celery deployment
2. **Load Balancing**: Multiple FastAPI instances
3. **Database Sharding**: Distribute job metadata
4. **CDN Integration**: Serve static files from CDN

---

This comprehensive documentation covers all aspects of the Sequence Alignment Tool. For specific implementation details, refer to the individual source files and their inline documentation.
