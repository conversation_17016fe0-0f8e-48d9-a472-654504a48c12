"""
BLAST Results File Storage Manager

This module handles file-based storage for BLAST job results, providing
utilities for saving, loading, and managing result files on disk.
"""

import os
import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class BlastResultsStorage:
    """Manages file-based storage for BLAST job results"""
    
    def __init__(self, base_dir: Optional[str] = None):
        """
        Initialize the storage manager
        
        Args:
            base_dir: Base directory for storing results. If None, uses environment variable
                     BLAST_RESULTS_DIR or defaults to './data/blast_results'
        """
        if base_dir is None:
            base_dir = os.getenv("BLAST_RESULTS_DIR", "./data/blast_results")
        
        self.base_dir = Path(base_dir)
        self.results_dir = self.base_dir / "results"
        self.metadata_dir = self.base_dir / "metadata"
        
        # Create directories if they don't exist
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.metadata_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"BlastResultsStorage initialized with base directory: {self.base_dir}")
    
    def save_job_result(self, job_id: str, result_data: Dict[Any, Any]) -> str:
        """
        Save BLAST job result data to a file
        
        Args:
            job_id: Unique job identifier
            result_data: Complete result data from BLAST search
            
        Returns:
            Path to the saved result file
        """
        try:
            result_file = self.results_dir / f"{job_id}.json"
            
            # Add metadata to result data
            result_data_with_meta = {
                "job_id": job_id,
                "saved_at": datetime.now().isoformat(),
                "file_version": "1.0",
                "data": result_data
            }
            
            # Save to file with pretty formatting
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result_data_with_meta, f, indent=2, default=str, ensure_ascii=False)
            
            logger.info(f"Saved BLAST results for job {job_id} to {result_file}")
            return str(result_file)
            
        except Exception as e:
            logger.error(f"Failed to save BLAST results for job {job_id}: {e}")
            raise
    
    def load_job_result(self, job_id: str) -> Optional[Dict[Any, Any]]:
        """
        Load BLAST job result data from file
        
        Args:
            job_id: Unique job identifier
            
        Returns:
            Result data dictionary or None if file doesn't exist
        """
        try:
            result_file = self.results_dir / f"{job_id}.json"
            
            if not result_file.exists():
                logger.warning(f"Result file not found for job {job_id}")
                return None
            
            with open(result_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            # Return just the data portion, not the metadata wrapper
            return file_data.get("data", file_data)
            
        except Exception as e:
            logger.error(f"Failed to load BLAST results for job {job_id}: {e}")
            return None
    
    def get_result_file_path(self, job_id: str) -> Optional[str]:
        """
        Get the file path for a job's results
        
        Args:
            job_id: Unique job identifier
            
        Returns:
            File path string or None if file doesn't exist
        """
        result_file = self.results_dir / f"{job_id}.json"
        return str(result_file) if result_file.exists() else None
    
    def delete_job_result(self, job_id: str) -> bool:
        """
        Delete BLAST job result file
        
        Args:
            job_id: Unique job identifier
            
        Returns:
            True if file was deleted, False if file didn't exist
        """
        try:
            result_file = self.results_dir / f"{job_id}.json"
            
            if result_file.exists():
                result_file.unlink()
                logger.info(f"Deleted BLAST results file for job {job_id}")
                return True
            else:
                logger.warning(f"Result file not found for job {job_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete BLAST results for job {job_id}: {e}")
            return False
    
    def get_result_summary(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a summary of the result without loading the full data
        
        Args:
            job_id: Unique job identifier
            
        Returns:
            Summary dictionary with basic statistics
        """
        try:
            result_file = self.results_dir / f"{job_id}.json"
            
            if not result_file.exists():
                return None
            
            # Get file stats
            file_stats = result_file.stat()
            file_size = file_stats.st_size
            modified_time = datetime.fromtimestamp(file_stats.st_mtime)
            
            # Load just enough to get basic info
            with open(result_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            data = file_data.get("data", file_data)
            
            summary = {
                "job_id": job_id,
                "file_size_bytes": file_size,
                "file_size_mb": round(file_size / (1024 * 1024), 2),
                "modified_at": modified_time.isoformat(),
                "total_hits": len(data.get("hits", [])),
                "database": data.get("database", "unknown"),
                "search_time": data.get("search_time"),
                "has_results": len(data.get("hits", [])) > 0
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get result summary for job {job_id}: {e}")
            return None
    
    def cleanup_old_results(self, days_old: int = 7) -> int:
        """
        Clean up result files older than specified days
        
        Args:
            days_old: Number of days after which to delete files
            
        Returns:
            Number of files deleted
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            deleted_count = 0
            
            for result_file in self.results_dir.glob("*.json"):
                file_stats = result_file.stat()
                file_modified = datetime.fromtimestamp(file_stats.st_mtime)
                
                if file_modified < cutoff_date:
                    result_file.unlink()
                    deleted_count += 1
                    logger.info(f"Deleted old result file: {result_file}")
            
            logger.info(f"Cleanup completed: deleted {deleted_count} files older than {days_old} days")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old results: {e}")
            return 0
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics
        
        Returns:
            Dictionary with storage statistics
        """
        try:
            result_files = list(self.results_dir.glob("*.json"))
            total_files = len(result_files)
            
            total_size = sum(f.stat().st_size for f in result_files)
            total_size_mb = round(total_size / (1024 * 1024), 2)
            
            return {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": total_size_mb,
                "results_directory": str(self.results_dir),
                "directory_exists": self.results_dir.exists()
            }
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            return {
                "total_files": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "results_directory": str(self.results_dir),
                "directory_exists": False,
                "error": str(e)
            }


# Global storage instance
blast_storage = BlastResultsStorage()
