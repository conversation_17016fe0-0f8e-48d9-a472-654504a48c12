import subprocess
import tempfile
import os
import json
from datetime import datetime
from celery.utils.log import get_task_logger

# Import celery_app at the end to avoid circular imports
from celery_app import celery_app
from blast_storage import blast_storage

# Get Celery task logger
logger = get_task_logger(__name__)


BINDIR = os.getenv("BINDIR", "/app/bin")
blastp = os.path.join(BINDIR, "blastp")
BLASTDB = os.getenv("BLASTDB", "")

logger.info(f"BLAST configuration - BINDIR: {BINDIR}")
logger.info(f"BLAST configuration - blastp path: {blastp}")
logger.info(f"BLAST configuration - blastp exists: {os.path.exists(blastp)}")

@celery_app.task(bind=True)
def run_blast_search(self, job_id: str, sequence: str, parameters: dict):
    """
    Execute BLAST search using blastp command
    """
    logger.info("=" * 80)
    logger.info("BLAST TASK STARTED")
    logger.info("=" * 80)
    logger.info(f"Starting BLAST task for job {job_id}")
    logger.info(f"Sequence length: {len(sequence)}")
    logger.info(f"Parameters: {parameters}")
    logger.info("=" * 80)

    # Set default parameters if none provided
    if parameters is None:
        parameters = {
            'evalue': 0.001,
            'max_target_seqs': 200,
            'word_size': 5,
            'matrix': 'BLOSUM62',
            'gap_open': 11,
            'gap_extend': 1,
            'database': 'refseq_protein'
        }

    # Ensure database parameter exists and is valid
    valid_databases = ['nr', 'refseq_protein']
    selected_database = parameters.get('database', 'refseq_protein')
    if selected_database not in valid_databases:
        logger.warning(f"Invalid database '{selected_database}', using 'refseq_protein' instead")
        selected_database = 'refseq_protein'

    logger.info(f"Selected database: {selected_database}")

    try:
        # Update task status to STARTED (this should trigger "running" status in the main app)
        self.update_state(state='STARTED', meta={
            'status': 'Starting BLAST search',
            'job_id': job_id,
            'current_step': 'initializing'
        })
        logger.info(f"Task state updated to STARTED for job {job_id}")

        # Create temporary files for input and output
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fasta', delete=False) as query_file:
            # Write sequence in FASTA format
            query_file.write(f">query_{job_id}\n{sequence}\n")
            query_file_path = query_file.name

        print(f"query file path: {query_file_path}")
        # Create temporary output file
        output_file = tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False)
        output_file_path = output_file.name
        output_file.close()

        try:
            # Update status to show we're actively running BLAST
            self.update_state(state='PROGRESS', meta={
                'status': 'Running BLAST search',
                'job_id': job_id,
                'current_step': 'executing_blast',
                'database': selected_database
            })
            logger.info(f"Starting BLAST execution for job {job_id}")


            # Real BLAST command:
            blast_cmd = [
                blastp,
                '-query', query_file_path,
                '-db', selected_database,  # Use selected database
                '-outfmt', '5',  # XML output format
                '-out', output_file_path,
                '-evalue', str(parameters['evalue']),
                '-max_target_seqs', str(parameters['max_target_seqs']),
                '-word_size', str(parameters['word_size']),
                '-matrix', parameters['matrix'],
                '-gapopen', str(parameters['gap_open']),
                '-gapextend', str(parameters['gap_extend']),
                '-num_threads', '5'
            ]
            logger.info(f"Running BLAST command: {' '.join(blast_cmd)}")
            logger.info(f"BLASTDB environment: {BLASTDB}")

            result = subprocess.run(
                blast_cmd,
                capture_output=True,
                text=True,
                timeout=7200  # 120 minutes timeout
            )

            if result.returncode != 0:
                error_msg = f"BLAST failed with return code {result.returncode}"
                if result.stderr:
                    error_msg += f": {result.stderr}"
                if result.stdout:
                    error_msg += f"\nSTDOUT: {result.stdout}"

                logger.error(f"BLAST command failed: {error_msg}")

                raise Exception(error_msg)
            
            # Try to read and parse real BLAST results
            try:
                with open(output_file_path, 'r') as f:
                    blast_output = f.read()

                logger.info(f"BLAST output file size: {len(blast_output)} characters")

                parsed_results = parse_blast_xml(blast_output)
                logger.info(f"Parsed {len(parsed_results.get('hits', []))} hits from XML")

            except Exception as parse_error:
                logger.error(f"Failed to parse BLAST XML: {parse_error}")
                raise parse_error

            logger.info(f"Final results have {len(parsed_results.get('hits', []))} hits")

            # Update status
            self.update_state(state='PROGRESS', meta={'status': 'Processing results'})

            # Save large result data to file
            try:
                result_file_path = blast_storage.save_job_result(job_id, parsed_results)
                logger.info(f"Saved BLAST results to file: {result_file_path}")

                # Get result summary for quick access
                result_summary = blast_storage.get_result_summary(job_id)

                result = {
                    'job_id': job_id,
                    'status': 'completed',
                    'completed_at': datetime.now().isoformat(),
                    'result_file_path': result_file_path,
                    'result_summary': result_summary,
                    'parameters': parameters
                }
                logger.info(f"Task completed successfully, result saved to file")
                return result

            except Exception as storage_error:
                logger.error(f"Failed to save results to file: {storage_error}")
                # Fallback: return results in memory (for backward compatibility)
                result = {
                    'job_id': job_id,
                    'status': 'completed',
                    'completed_at': datetime.now().isoformat(),
                    'results': parsed_results,
                    'parameters': parameters,
                    'storage_error': str(storage_error)
                }
                return result

        finally:
            # Clean up temporary files
            try:
                os.unlink(query_file_path)
                os.unlink(output_file_path)
            except:
                pass

    except subprocess.TimeoutExpired as e:
        error_msg = 'BLAST search timed out after 120 minutes'
        logger.error(f"Task timeout: {error_msg}")
        self.update_state(
            state='FAILURE',
            meta={
                'error': error_msg,
                'error_type': 'TimeoutError',
                'job_id': job_id
            }
        )
        return {
            'job_id': job_id,
            'status': 'failed',
            'error': error_msg,
            'error_type': 'timeout'
        }
    except Exception as e:
        error_msg = f"BLAST task failed: {str(e)}"
        error_type = type(e).__name__
        logger.error(f"Task exception ({error_type}): {error_msg}")
        logger.debug(f"Full exception details: {repr(e)}")

        self.update_state(
            state='FAILURE',
            meta={
                'error': error_msg,
                'error_type': error_type,
                'job_id': job_id
            }
        )
        return {
            'job_id': job_id,
            'status': 'failed',
            'error': error_msg,
            'error_type': error_type
        }


def parse_blast_xml(xml_content: str) -> dict:
    """
    Simple XML parser for BLAST results
    In production, use BioPython's NCBIXML parser
    """
    try:
        import xml.etree.ElementTree as ET
        
        root = ET.fromstring(xml_content)
        
        results = {
            'query_length': 0,
            'hits': [],
            'statistics': {}
        }
        
        # Extract query length
        query_len_elem = root.find('.//BlastOutput_query-len')
        if query_len_elem is not None:
            results['query_length'] = int(query_len_elem.text)
        
        # Extract hits
        for iteration in root.findall('.//Iteration'):
            for hit in iteration.findall('.//Hit'):
                hit_data = {}
                
                # Hit ID and description
                hit_id_elem = hit.find('Hit_id')
                hit_def_elem = hit.find('Hit_def')
                hit_len_elem = hit.find('Hit_len')
                hit_accession_elem = hit.find('Hit_accession')

                if hit_id_elem is not None:
                    hit_data['id'] = hit_id_elem.text
                if hit_def_elem is not None:
                    hit_data['description'] = hit_def_elem.text
                    # Parse organism from description if available
                    if '[' in hit_def_elem.text and ']' in hit_def_elem.text:
                        organism_start = hit_def_elem.text.rfind('[')
                        organism_end = hit_def_elem.text.rfind(']')
                        if organism_start < organism_end:
                            hit_data['organism'] = hit_def_elem.text[organism_start+1:organism_end]
                if hit_len_elem is not None:
                    hit_data['length'] = int(hit_len_elem.text)
                if hit_accession_elem is not None:
                    hit_data['accession'] = hit_accession_elem.text
                
                # HSPs (High-scoring Segment Pairs)
                hit_data['hsps'] = []
                for hsp in hit.findall('.//Hsp'):
                    hsp_data = {}
                    
                    # Extract HSP data
                    for field in ['Hsp_score', 'Hsp_bit-score', 'Hsp_evalue',
                                'Hsp_query-from', 'Hsp_query-to',
                                'Hsp_hit-from', 'Hsp_hit-to',
                                'Hsp_identity', 'Hsp_positive', 'Hsp_align-len', 'Hsp_gaps']:
                        elem = hsp.find(field)
                        if elem is not None:
                            if field in ['Hsp_score', 'Hsp_query-from', 'Hsp_query-to',
                                       'Hsp_hit-from', 'Hsp_hit-to', 'Hsp_identity',
                                       'Hsp_positive', 'Hsp_align-len', 'Hsp_gaps']:
                                hsp_data[field.replace('Hsp_', '').replace('-', '_')] = int(elem.text)
                            elif field in ['Hsp_bit-score', 'Hsp_evalue']:
                                hsp_data[field.replace('Hsp_', '').replace('-', '_')] = float(elem.text)

                    # Calculate additional metrics
                    if 'identity' in hsp_data and 'align_len' in hsp_data:
                        hsp_data['identity_percent'] = round((hsp_data['identity'] / hsp_data['align_len']) * 100, 1)
                    if 'positive' in hsp_data and 'align_len' in hsp_data:
                        hsp_data['positive_percent'] = round((hsp_data['positive'] / hsp_data['align_len']) * 100, 1)
                    if 'gaps' in hsp_data and 'align_len' in hsp_data:
                        hsp_data['gaps_percent'] = round((hsp_data['gaps'] / hsp_data['align_len']) * 100, 1)
                    
                    # Extract alignment strings
                    for field in ['Hsp_qseq', 'Hsp_hseq', 'Hsp_midline']:
                        elem = hsp.find(field)
                        if elem is not None:
                            hsp_data[field.replace('Hsp_', '')] = elem.text
                    
                    hit_data['hsps'].append(hsp_data)
                
                if hit_data['hsps']:  # Only add hits with HSPs
                    results['hits'].append(hit_data)
        
        # Sort hits by best E-value
        if results['hits']:
            results['hits'].sort(key=lambda x: min(hsp['evalue'] for hsp in x['hsps']) if x['hsps'] else float('inf'))
        
        return results
        
    except Exception as e:
        return {
            'error': f'Failed to parse BLAST results: {str(e)}',
            'raw_xml': xml_content[:1000]  # First 1000 chars for debugging
        }
