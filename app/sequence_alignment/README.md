# NeoSeq: Advanced Sequence Alignment & BLAST Search Platform

A comprehensive web-based bioinformatics platform offering sequence alignment analysis and protein BLAST search capabilities with persistent job management and file-based result storage.

![Sequence Alignment Web Tool](img/main.png)

## 🚀 Features

### 🧬 FASTQ File Alignment
![Sequence Alignment Web Tool](img/fastq_align.png)

- Upload FASTQ files via drag-and-drop or file browser
- Align multiple sequences with custom reference sequences
- View alignment scores, mutations, and quality metrics
- Summary statistics and detailed results
- Download results as JSON
- Automatic detection of reverse complement sequences
- Skip leading and trailing gaps in alignment
- Quality score analysis with Phred scores

### 🔀 Sequence Alignment Tool
![Sequence Alignment Web Tool](img/two_seq_align.png)

#### Pairwise Alignment
- Direct input of two individual sequences (DNA, RNA, or protein)
- Customizable alignment parameters:
  - Match score (default: +1.0)
  - Mismatch score (default: -1.0)
  - Gap open penalty (default: -2.0)
  - Gap extension penalty (default: -0.5)
- Real-time mutation, insertion, and deletion highlighting
- Interactive parameter tuning
- Detailed alignment visualization with color coding
- Sequence length validation and character counting

#### Multiple Sequence Alignment (MUSCLE)
- **FASTA input support** - Enter multiple sequences in FASTA format or upload FASTA files
- **MUSCLE algorithm integration** - Industry-standard multiple sequence alignment
- **Configurable parameters**:
  - Maximum iterations (default: 16)
  - Gap penalty (default: -12)
- **Enhanced visualization**:
  - Color-coded residues by biochemical properties (hydrophobic, polar, charged, etc.)
  - **Smart coloring** - Only mismatched columns are colored, conserved regions remain clean
  - Position indicators showing alignment block starting positions
  - Conservation analysis with visual conservation bar
- **Auto-detection** of protein vs nucleotide sequences with appropriate color schemes
- **Download options** - Export results in FASTA or Clustal formats
- **Interactive legend** - Dynamic legend showing color coding based on sequence type

### 🔬 BLAST Protein Search (NEW)
- **Asynchronous BLAST searches** against protein databases using Celery queues
- **Real-time job monitoring** with status updates and progress tracking
- **Persistent job storage** - jobs survive server restarts and maintenance
- **File-based result storage** for handling large BLAST outputs efficiently
- **Comprehensive job management** with job listing, filtering, and detailed views
- **Multiple download formats** (JSON, CSV, TSV) for results
- **Configurable BLAST parameters** (E-value, max targets, scoring matrix, etc.)
- **Error handling and recovery** with detailed error reporting
- **Storage management** with automatic cleanup and statistics

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Redis server (for BLAST job queuing)
- BLAST+ tools (for protein searches)
- MUSCLE (for multiple sequence alignment)

### Quick Start

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start all services:**
   ```bash
   ./start_with_celery.sh
   ```
   This will start:
   - Redis server (port 6380)
   - FastAPI backend (port 5001)
   - Celery worker for BLAST tasks

3. **Access the application:**
   ```
   http://localhost:5001
   ```

### Manual Setup

1. **Start Redis server:**
   ```bash
   redis-server --port 6380 --daemonize yes
   ```

2. **Start the FastAPI backend:**
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 5001 --reload
   ```

3. **Start Celery worker (in another terminal):**
   ```bash
   celery -A celery_app worker --loglevel=info
   ```

### Environment Configuration

```bash
# Storage directory for BLAST results (default: ./data/blast_results)
export BLAST_RESULTS_DIR="/path/to/blast/storage"

# Redis connection (default: redis://localhost:6380/0)
export REDIS_URL="redis://localhost:6380/0"

# BLAST+ and MUSCLE binaries directory
export BINDIR="/usr/bin"

# BLAST database path
export BLASTDB="/path/to/blast/databases"
```

## 📖 Usage Guide

### FASTQ File Alignment

1. **Navigate to FASTQ Alignment:**
   - From the main page, click "Start FASTQ Alignment"
   - Or go directly to `/fastq-alignment`

2. **Upload FASTQ File:**
   - Drag and drop your `.fastq` or `.fq` file onto the upload area
   - Or click "Choose File" to browse for your file

3. **Enter Reference Sequence:**
   - Paste your reference DNA/RNA sequence in the text area
   - The sequence will be automatically cleaned (whitespace removed, converted to uppercase)

4. **Click "Align Sequences":**
   - The tool will process each sequence in your FASTQ file
   - Both forward and reverse complement orientations are tested
   - The best alignment is selected for each sequence

5. **View Results:**
   - **Summary:** Overview statistics including total sequences, average scores, and high-scoring sequences
   - **Detailed Results:** Individual sequence information with mutations, quality scores, and alignment details
   - **Download:** Export results as JSON for further analysis

### Sequence Alignment Tool

1. **Navigate to Sequence Alignment:**
   - From the main page, click "Start Sequence Alignment"
   - Or go directly to `/sequence-alignment`

#### Pairwise Alignment Tab

2. **Enter Two Sequences:**
   - Input your first sequence (reference) in the top text box
   - Input your second sequence (query) in the bottom text box
   - Sequences can be DNA, RNA, or protein

3. **Configure Parameters (Optional):**
   - Click "Configure" to expand parameter settings
   - Adjust match score, mismatch score, gap open penalty, and gap extension penalty
   - Default values work well for most use cases

4. **Click "Align Sequences":**
   - The tool will perform pairwise alignment with your specified parameters
   - Results are displayed immediately

5. **View Results:**
   - **Summary:** Alignment score, number of mutations, and sequence information
   - **Detailed Alignment:** Visual representation with highlighted differences
   - **Color Coding:** Yellow for mutations, green for insertions, red for deletions

#### Multiple Sequence Alignment Tab

6. **Switch to Multiple Sequence Alignment:**
   - Click the "Multiple Sequence Alignment (MUSCLE)" tab

7. **Input Multiple Sequences:**
   - **Text Input:** Enter sequences in FASTA format in the text area
   - **File Upload:** Or upload a FASTA file (.fasta, .fa, .fas, .txt)
   - Each sequence should start with a header line beginning with '>'

8. **Configure MUSCLE Parameters (Optional):**
   - **Max Iterations:** Maximum number of refinement iterations (default: 16)
   - **Gap Penalty:** Penalty for opening gaps (default: -12)

9. **Click "Run MUSCLE Alignment":**
   - The tool will perform multiple sequence alignment using the MUSCLE algorithm
   - Processing may take a few moments depending on sequence length and number

10. **View Enhanced Results:**
    - **Conservation Analysis:** Visual bar showing conservation levels across positions
    - **Color-Coded Alignment:**
      - Only mismatched columns are colored for better readability
      - Protein sequences: Hydrophobic (blue), Polar (green), Positive (red), Negative (orange), Glycine (purple)
      - Nucleotide sequences: A (red), T/U (teal), G (blue), C (green), N (plum)
    - **Position Indicators:** Starting positions shown for each alignment block
    - **Interactive Legend:** Dynamic legend based on sequence type (protein vs nucleotide)
    - **Download Options:** Export results in FASTA or Clustal formats

### BLAST Protein Search (NEW)

1. **Navigate to BLAST Search:**
   - From the main page, click "BLAST Protein Search"
   - Or go directly to `/blast-search`

2. **Submit a BLAST Job:**
   - Enter your protein sequence in FASTA format
   - Provide a job name and email (optional)
   - Configure BLAST parameters:
     - E-value threshold (default: 10.0)
     - Maximum target sequences (default: 100)
     - Word size (default: 3)
     - Scoring matrix (default: BLOSUM62)
     - Gap penalties

3. **Monitor Job Progress:**
   - Jobs are processed asynchronously in the background
   - Real-time status updates (pending → running → completed/failed)
   - View all submitted jobs in the job list
   - Jobs persist across server restarts

4. **View Results:**
   - Click on completed jobs to view detailed results
   - Browse hit sequences with alignment details
   - View E-values, bit scores, and sequence alignments
   - Download results in multiple formats (JSON, CSV, TSV)

5. **Job Management:**
   - View job history and status
   - Delete old jobs and results
   - Monitor storage usage
   - Automatic cleanup of old files

## 🌐 API Endpoints

### Core Alignment Endpoints
- `GET /` - Main page with functionality overview
- `GET /fastq-alignment` - FASTQ file alignment interface
- `GET /sequence-alignment` - Unified sequence alignment interface (pairwise + multiple)
- `POST /align` - FASTQ alignment endpoint (accepts FASTQ file + reference sequence)
- `POST /align-two-sequences` - Pairwise alignment endpoint (accepts two sequences + parameters)
- `POST /align-multiple-sequences` - Multiple sequence alignment endpoint (accepts FASTA sequences + MUSCLE parameters)
- `GET /sequence-details` - Sequence details page

### BLAST Search Endpoints (NEW)
- `GET /blast-search` - BLAST search interface
- `POST /blast` - Submit BLAST job (accepts protein sequence + parameters)
- `GET /blast-jobs` - List all BLAST jobs with status and metadata
- `GET /blast-job/{job_id}` - Get specific job status and summary
- `GET /blast-job/{job_id}/results` - Get full BLAST results from file storage
- `GET /blast-job/{job_id}/download` - Download results in various formats (JSON, CSV, TSV)
- `DELETE /blast-job/{job_id}/results` - Delete job results from storage

### Storage Management Endpoints (NEW)
- `GET /storage/stats` - Get storage statistics (files, disk usage, job counts)
- `POST /storage/cleanup` - Clean up old result files and metadata
- `GET /health` - Health check and system status

## 📁 File Structure

```
.
├── main.py                          # FastAPI backend with all endpoints
├── celery_app.py                    # Celery configuration for async job processing
├── blast_tasks.py                   # BLAST job execution and result processing
├── blast_storage.py                 # File-based storage for BLAST results
├── job_metadata_storage.py          # Persistent job metadata management
├── start_with_celery.sh            # Complete service startup script
├── run_with_root_path.py           # Server with root path support
├── requirements.txt                # Python dependencies
├── README.md                       # This documentation
├── BLAST_INTEGRATION.md            # BLAST system documentation
├── FILE_STORAGE.md                 # File storage system documentation
├── PERSISTENT_JOBS.md              # Persistent job storage documentation
├── data/                           # Data storage directory
│   └── blast_results/              # BLAST results and metadata
│       ├── metadata/               # Job metadata (jobs.json)
│       └── results/                # Result files (job_uuid.json)
└── static/                         # Frontend files
    ├── index.html                  # Main page with functionality overview
    ├── fastq-alignment.html        # FASTQ file alignment interface
    ├── sequence-alignment.html     # Unified sequence alignment interface (pairwise + multiple)
    ├── blast-search.html           # BLAST search interface (NEW)
    ├── blast-job-details.html      # BLAST job details page (NEW)
    ├── sequence-details.html       # Sequence details page
    ├── fastq-alignment.js # FASTQ alignment JavaScript
    ├── sequence-alignment.js       # Unified sequence alignment JavaScript (pairwise + MUSCLE)
    ├── sequence-details.js         # Sequence details JavaScript
    ├── path-utils.js               # URL path utilities
    ├── styles.css                  # Main stylesheet
    └── sequence-details.css        # Sequence details stylesheet
```

## 🔧 Technical Details

### Alignment Algorithms
- **Pairwise Alignment:** Uses BioPython's PairwiseAligner with global alignment strategy
- **Multiple Sequence Alignment:** Uses MUSCLE (MUltiple Sequence Comparison by Log-Expectation) algorithm
- **FASTQ Alignment:** Fixed scoring (Match=+1, Mismatch=-1, Gap open=-2, Gap extend=-0.5)
- **Pairwise Alignment:** Configurable scoring parameters
- **MUSCLE Parameters:** Configurable iterations and gap penalties
- Tests both forward and reverse complement orientations (FASTQ only)
- Selects the alignment with the highest score

### BLAST Integration (NEW)
- **Asynchronous Processing:** Uses Celery with Redis for background job execution
- **Real BLAST+ Integration:** Supports local BLAST+ installations and databases
- **Configurable Parameters:** E-value, max targets, word size, scoring matrix, gap penalties
- **XML Result Parsing:** Processes BLAST XML output into structured data
- **Error Handling:** Comprehensive error detection and reporting
- **Timeout Management:** 30-minute timeout with graceful failure handling

### Storage Architecture (NEW)
- **Dual Storage System:** In-memory cache + persistent file storage
- **File-Based Results:** Large BLAST results stored as JSON files on disk
- **Persistent Metadata:** Job information stored in `jobs.json` with file locking
- **Atomic Operations:** Safe concurrent access with temporary file operations
- **Storage Management:** Automatic cleanup, statistics, and monitoring

### Job Management (NEW)
- **Persistent Jobs:** Jobs survive server restarts and maintenance
- **Status Tracking:** Real-time status updates (pending → running → completed/failed)
- **Result Caching:** Efficient storage and retrieval of large result datasets
- **Cleanup Policies:** Configurable retention and automatic cleanup
- **Monitoring:** Storage statistics, job counts, and system health

### Enhanced Visualization Features (NEW)
- **Smart Color Coding:** Only mismatched columns are colored, conserved regions remain clean
- **Biochemical Property Colors:**
  - **Proteins:** Hydrophobic (blue), Polar (green), Positive (red), Negative (orange), Glycine (purple), Gaps (gray)
  - **Nucleotides:** A (red), T/U (teal), G (blue), C (green), N (plum), Gaps (gray)
- **Auto-Detection:** Automatically detects protein vs nucleotide sequences
- **Position Indicators:** Clean position markers instead of repeating digit rulers
- **Interactive Legend:** Dynamic legend that adapts to sequence type
- **Conservation Analysis:** Visual conservation bar for multiple sequence alignments
- **Improved Readability:** Better spacing, fonts, and layout for large alignments

### Mutation Detection
- Identifies substitutions, insertions, and deletions between sequences
- Skips leading and trailing gaps in the reference sequence
- Reports position, reference base, and alternative base
- Uses 1-based positioning for mutations
- Visual highlighting in detailed alignment view

### Quality Metrics (FASTQ Only)
- Average Phred quality score per sequence
- Minimum Phred quality score per sequence
- Sequence length and alignment score
- GC content analysis

### Parameter Customization
- **Two-Sequence Alignment:** Real-time parameter adjustment
- **BLAST Search:** Comprehensive parameter configuration
- **Storage Settings:** Configurable retention and cleanup policies
- **Performance Tuning:** Adjustable timeouts and concurrency limits

### Deployment Features
- **Root Path Support:** Automatic detection and reverse proxy compatibility
- **Environment Configuration:** Comprehensive environment variable support
- **Service Management:** Integrated startup scripts for all components
- **Health Monitoring:** System health checks and status reporting

## 📊 Example Usage

### Sample Protein Sequence for BLAST
```
>Example protein sequence
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
```

### Sample Multiple Sequence Alignment (FASTA)
```
>Sequence1
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
>Sequence2
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGV
>Sequence3
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGA
>Sequence4
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGT
```

### Sample FASTQ Data
You can test the FASTQ alignment tool with sample data. The reference sequence should be in DNA format (A, T, G, C).

## 🔧 Troubleshooting

### General Issues
1. **Service Startup:**
   - Ensure Redis is running: `redis-cli ping`
   - Check port availability: `lsof -i :5001`
   - Verify all dependencies: `pip list`

2. **BLAST Issues:**
   - Check BLAST+ installation: `blastp -version`
   - Verify database path: `ls $BLASTDB`
   - Monitor Celery worker logs for errors

3. **Storage Issues:**
   - Check disk space: `df -h`
   - Verify write permissions: `ls -la data/`
   - Monitor storage stats: `curl localhost:5001/storage/stats`

### File Upload Issues
- Ensure your file has `.fastq` or `.fq` extension
- Check that the file is properly formatted FASTQ
- Verify file size limits (default: 100MB)

### Alignment Errors
- Verify the reference sequence contains only valid DNA bases
- Ensure the reference sequence is not empty
- Check sequence format and encoding

### BLAST Search Issues
- Verify protein sequence format (single letter amino acid codes)
- Check BLAST database availability and permissions
- Monitor job status for detailed error messages
- Review Celery worker logs for processing errors

## 🚀 Development

### Development Mode
```bash
# Start with auto-reload
uvicorn main:app --reload --host 0.0.0.0 --port 5001

# Start Celery worker with auto-reload
celery -A celery_app worker --loglevel=debug --reload
```

### Testing
```bash
# Test basic functionality
curl http://localhost:5001/health

# Test storage stats
curl http://localhost:5001/storage/stats

# Submit test BLAST job
curl -X POST http://localhost:5001/blast \
  -H "Content-Type: application/json" \
  -d '{"sequence": "MKTVRQERLK", "job_name": "Test Job"}'
```

### Monitoring
- **Application Logs:** Check FastAPI and Celery output
- **Storage Usage:** Monitor `/storage/stats` endpoint
- **Job Status:** Use `/blast-jobs` endpoint for job monitoring
- **System Health:** Use `/health` endpoint for status checks

## 📚 Documentation

- **[BLAST_INTEGRATION.md](BLAST_INTEGRATION.md)** - Detailed BLAST system documentation
- **[FILE_STORAGE.md](FILE_STORAGE.md)** - File-based storage system guide
- **[PERSISTENT_JOBS.md](PERSISTENT_JOBS.md)** - Persistent job management documentation

## 🏗️ Production Deployment

### Requirements
- Python 3.8+
- Redis server
- BLAST+ tools and databases
- Sufficient disk space for result storage
- Process manager (systemd, supervisor, etc.)

### Recommended Setup
```bash
# Use production WSGI server
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker

# Use multiple Celery workers
celery -A celery_app worker --concurrency=4

# Configure Redis persistence
redis-server --appendonly yes --save 900 1
```

### Security Considerations
- Configure firewall rules for port access
- Set up proper file permissions for storage directories
- Use environment variables for sensitive configuration
- Implement rate limiting for API endpoints
- Regular security updates for all dependencies

## 📄 License

This project is open source and available under the MIT License.
