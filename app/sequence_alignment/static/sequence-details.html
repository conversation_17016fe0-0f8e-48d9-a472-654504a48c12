<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sequence Details - Sequence Alignment Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/sequence-details.css" rel="stylesheet">
</head>
<body>
    <!-- Back Button -->
    <!-- <button class="btn btn-outline-primary back-button" onclick="goBack()">
        <i class="fas fa-arrow-left"></i> Back to Results
    </button> -->

    <div class="container mt-5">
        <!-- Loading Indicator -->
        <div class="loading" id="loadingDiv">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading sequence details...</p>
        </div>

        <!-- Error Message -->
        <div class="alert alert-warning error-message" id="errorMessage">
            <h5><i class="fas fa-exclamation-triangle"></i> Feature Under Development</h5>
            <p>The sequence details feature requires storing sequence data from alignment results. This will be implemented in a future version.</p>
            <p><strong>Sequence ID:</strong> <span id="errorSequenceId"></span></p>
        </div>

        <!-- Sequence Details Content -->
        <div id="sequenceContent" style="display: none;">
            <!-- Sequence Header -->
            <div class="sequence-header">
                <div class="row">
                    <div class="col-md-8">
                        <h2><i class="fas fa-dna"></i> <span id="sequenceId">Loading...</span></h2>
                        <p class="mb-0">Detailed sequence analysis and information</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div id="sequenceBadges">
                            <!-- Badges will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs nav-justified" id="detailsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview-pane" type="button" role="tab">
                        <i class="fas fa-info-circle"></i> Overview
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sequence-tab" data-bs-toggle="tab" data-bs-target="#sequence-pane" type="button" role="tab">
                        <i class="fas fa-dna"></i> Full Sequence
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="alignment-tab" data-bs-toggle="tab" data-bs-target="#alignment-pane" type="button" role="tab">
                        <i class="fas fa-align-left"></i> Alignment
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="quality-tab" data-bs-toggle="tab" data-bs-target="#quality-pane" type="button" role="tab">
                        <i class="fas fa-chart-line"></i> Quality
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="detailsTabContent">
                <!-- Overview Tab -->
                <div class="tab-pane fade show active" id="overview-pane" role="tabpanel">
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <h6><i class="fas fa-ruler"></i> Sequence Information</h6>
                                    <p><strong>Length:</strong> <span id="sequenceLength">-</span> bp</p>
                                    <p><strong>GC Content:</strong> <span id="gcContent">-</span>%</p>
                                    <p><strong>Reverse Complement:</strong> <span id="reverseComplement">-</span></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <h6><i class="fas fa-chart-bar"></i> Alignment Metrics</h6>
                                    <p><strong>Alignment Score:</strong> <span id="alignmentScore">-</span></p>
                                    <p><strong>Mutations:</strong> <span id="mutationCount">-</span></p>
                                    <p><strong>Insertions:</strong> <span id="insertionStatus">-</span></p>
                                    <p><strong>Deletions:</strong> <span id="deletionStatus">-</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <h6><i class="fas fa-star"></i> Quality Metrics</h6>
                                    <p><strong>Average Quality:</strong> <span id="avgQuality">-</span></p>
                                    <p><strong>Minimum Quality:</strong> <span id="minQuality">-</span></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <h6><i class="fas fa-exclamation-triangle"></i> Mutations Found</h6>
                                    <div id="mutationsList">
                                        <!-- Mutations will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Full Sequence Tab -->
                <div class="tab-pane fade" id="sequence-pane" role="tabpanel">
                    <div class="mt-3">
                        <h6><i class="fas fa-dna"></i> Original Full-Length Sequence</h6>
                        <div class="sequence-display" id="fullSequence">
                            <!-- Full sequence will be displayed here -->
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary" id="copySequenceBtn">
                                <i class="fas fa-copy"></i> Copy Sequence
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="downloadSequence()">
                                <i class="fas fa-download"></i> Download FASTA
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Alignment Tab -->
                <div class="tab-pane fade" id="alignment-pane" role="tabpanel">
                    <div class="mt-3">
                        <h6><i class="fas fa-align-left"></i> Sequence Alignment</h6>
                        <div id="alignmentDisplay">
                            <!-- Alignment will be displayed here -->
                        </div>
                    </div>
                </div>

                <!-- Quality Tab -->
                <div class="tab-pane fade" id="quality-pane" role="tabpanel">
                    <div class="mt-3">
                        <h6><i class="fas fa-chart-line"></i> Quality Score Distribution</h6>
                        <div id="qualityChart">
                            <!-- Quality visualization will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/path-utils.js"></script>
    <script src="static/sequence-details.js"></script>
</body>
</html>
