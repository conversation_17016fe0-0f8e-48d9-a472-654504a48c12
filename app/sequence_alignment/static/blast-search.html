<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BLAST Protein Search</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sequence-input {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        .status-pending { background-color: #ffc107; }
        .status-running { background-color: #17a2b8; }
        .status-completed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .hit-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .hit-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .sequence-display {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            word-break: break-all;
            white-space: pre-wrap;
        }
        .loading-spinner {
            display: none;
        }
        .results-section {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-search text-warning"></i> BLAST Protein Search</h1>
                        <p class="text-muted">Search protein sequences against the nr database using BLASTP</p>
                    </div>
                    <a href="/" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                </div>
            </div>
        </div>

        <!-- Search Form -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-dna"></i> Submit BLAST Search</h5>
                    </div>
                    <div class="card-body">
                        <form id="blastForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="jobName" class="form-label">Job Name (optional)</label>
                                        <input type="text" class="form-control" id="jobName" placeholder="Enter a name for this search">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email (optional)</label>
                                        <input type="email" class="form-control" id="email" placeholder="Enter your email for notifications">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="sequence" class="form-label">Protein Sequence</label>
                                <textarea class="form-control sequence-input" id="sequence" rows="8"
                                    placeholder="Enter your protein sequence in FASTA format or plain text..." required></textarea>
                                <div class="form-text">
                                    Paste sequence without FASTA header.
                                    <button type="button" class="btn btn-link btn-sm p-0" onclick="loadExample()">
                                        Load example sequence
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="database" class="form-label">Database</label>
                                <select class="form-select" id="database">
                                    <option value="refseq_protein" selected>RefSeq Protein (Reference sequences)</option>
                                    <option value="nr">nr (Non-redundant protein sequences) - Note: very slow!!!</option>
                                </select>
                                <div class="form-text">Choose the protein database to search against</div>
                            </div>

                            <!-- Advanced Parameters Section -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <button class="btn btn-link p-0 text-decoration-none" type="button" data-bs-toggle="collapse" data-bs-target="#advancedParams" aria-expanded="false">
                                            <i class="fas fa-cog"></i> Advanced Parameters (Optional)
                                            <i class="fas fa-chevron-down ms-2"></i>
                                        </button>
                                    </h6>
                                </div>
                                <div class="collapse" id="advancedParams">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="evalue" class="form-label">E-value Threshold</label>
                                                    <input type="number" class="form-control" id="evalue" value="10" step="any" min="0">
                                                    <div class="form-text">Lower values = more stringent (default: 10)</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="maxTargetSeqs" class="form-label">Max Target Sequences</label>
                                                    <input type="number" class="form-control" id="maxTargetSeqs" value="100" min="1" max="2000">
                                                    <div class="form-text">Maximum number of hits to return (default: 100)</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="wordSize" class="form-label">Word Size</label>
                                                    <select class="form-select" id="wordSize">
                                                        <option value="3">3 (more sensitive)</option>
                                                        <option value="5" selected>5 (default)</option>
                                                        <option value="6">6 (faster)</option>
                                                    </select>
                                                    <div class="form-text">Smaller = more sensitive, larger = faster</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Empty column for layout balance -->
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="matrix" class="form-label">Scoring Matrix</label>
                                                    <select class="form-select" id="matrix">
                                                        <option value="BLOSUM45">BLOSUM45 (distant relationships)</option>
                                                        <option value="BLOSUM62" selected>BLOSUM62 (default)</option>
                                                        <option value="BLOSUM80">BLOSUM80 (close relationships)</option>
                                                        <option value="PAM30">PAM30 (very close relationships)</option>
                                                        <option value="PAM70">PAM70 (moderate relationships)</option>
                                                        <option value="PAM250">PAM250 (distant relationships)</option>
                                                    </select>
                                                    <div class="form-text">Choose based on expected evolutionary distance</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Empty column for layout balance -->
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="gapOpen" class="form-label">Gap Open Penalty</label>
                                                    <input type="number" class="form-control" id="gapOpen" value="11" min="1" max="50">
                                                    <div class="form-text">Cost to open a gap (default: 11)</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="gapExtend" class="form-label">Gap Extend Penalty</label>
                                                    <input type="number" class="form-control" id="gapExtend" value="1" min="1" max="10">
                                                    <div class="form-text">Cost to extend a gap (default: 1)</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Tip:</strong> Default parameters work well for most searches. Adjust only if you need specific sensitivity or speed requirements.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-warning btn-lg" id="submitBtn">
                                    <i class="fas fa-search"></i> Submit BLAST Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Section -->
        <div class="row mt-4 loading-spinner" id="loadingSection">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Submitting BLAST job...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Section -->
        <div class="row mt-4 results-section" id="statusSection">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Job Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="statusDisplay"></div>
                        <div id="jobInfo" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="row mt-4 results-section" id="resultsSection">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> BLAST Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="resultsDisplay"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- All Jobs Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> All BLAST Jobs</h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshJobsList()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="jobsListContainer">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading jobs...</span>
                                </div>
                                <p class="mt-2">Loading jobs...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- About Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> About BLAST Search</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-database"></i> Database</h6>
                                <p class="text-muted small">
                                    Searches against the nr (non-redundant) protein database using BLASTP algorithm.
                                </p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-cogs"></i> Parameters</h6>
                                <p class="text-muted small">
                                    Configurable parameters: E-value, max targets, word size, scoring matrix, gap penalties.
                                </p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-clock"></i> Processing Time</h6>
                                <p class="text-muted small">
                                    Typical search time: 10-60 minutes depending on sequence length and database load.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/path-utils.js"></script>
    <script>
        let currentJobId = null;
        let pollInterval = null;

        const exampleSequence = `MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG`;

        function loadExample() {
            document.getElementById('sequence').value = exampleSequence;
        }

        document.getElementById('blastForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const sequence = document.getElementById('sequence').value.trim();
            const jobName = document.getElementById('jobName').value.trim();
            const email = document.getElementById('email').value.trim();

            // Collect advanced parameters
            const database = document.getElementById('database').value || 'refseq_protein';
            const evalue = parseFloat(document.getElementById('evalue').value) || 10;
            const maxTargetSeqs = parseInt(document.getElementById('maxTargetSeqs').value) || 100;
            const wordSize = parseInt(document.getElementById('wordSize').value) || 5;
            const matrix = document.getElementById('matrix').value || 'BLOSUM62';
            const gapOpen = parseInt(document.getElementById('gapOpen').value) || 11;
            const gapExtend = parseInt(document.getElementById('gapExtend').value) || 1;

            if (!sequence) {
                alert('Please enter a protein sequence');
                return;
            }

            // Validate parameters
            if (evalue < 0) {
                alert('E-value must be non-negative');
                return;
            }
            if (maxTargetSeqs < 1 || maxTargetSeqs > 5000) {
                alert('Max target sequences must be between 1 and 5000');
                return;
            }

            // Show loading
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('submitBtn').disabled = true;

            try {
                const requestBody = {
                    sequence: sequence,
                    job_name: jobName || undefined,
                    email: email || undefined,
                    parameters: {
                        database: database,
                        evalue: evalue,
                        max_target_seqs: maxTargetSeqs,
                        word_size: wordSize,
                        matrix: matrix,
                        gap_open: gapOpen,
                        gap_extend: gapExtend
                    }
                };

                const response = await fetch(getApiPath('/submit-blast'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();

                if (response.ok) {
                    currentJobId = data.job_id;
                    showStatus();
                    startPolling();
                } else {
                    alert(`Error: ${data.detail || 'Failed to submit job'}`);
                }
            } catch (error) {
                alert(`Error: ${error.message}`);
            } finally {
                document.getElementById('loadingSection').style.display = 'none';
                document.getElementById('submitBtn').disabled = false;
            }
        });

        function showStatus() {
            document.getElementById('statusSection').style.display = 'block';
            document.getElementById('jobInfo').innerHTML = `
                <p><strong>Job ID:</strong> <code>${currentJobId}</code></p>
                <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
            `;
        }

        function updateJobInfo(jobData) {
            const jobInfo = document.getElementById('jobInfo');
            let parametersHtml = '';

            if (jobData.parameters) {
                parametersHtml = `
                    <div class="mt-3">
                        <h6>Search Parameters:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small><strong>Database:</strong> ${jobData.parameters.database || 'refseq_protein'}</small><br>
                                <small><strong>E-value:</strong> ${jobData.parameters.evalue}</small><br>
                                <small><strong>Max targets:</strong> ${jobData.parameters.max_target_seqs}</small><br>
                                <small><strong>Word size:</strong> ${jobData.parameters.word_size}</small>
                            </div>
                            <div class="col-md-6">
                                <small><strong>Matrix:</strong> ${jobData.parameters.matrix}</small><br>
                                <small><strong>Gap open:</strong> ${jobData.parameters.gap_open}</small><br>
                                <small><strong>Gap extend:</strong> ${jobData.parameters.gap_extend}</small>
                            </div>
                        </div>
                    </div>
                `;
            }

            jobInfo.innerHTML = `
                <p><strong>Job ID:</strong> <code>${jobData.job_id}</code></p>
                <p><strong>Job Name:</strong> ${jobData.job_name}</p>
                <p><strong>Submitted:</strong> ${new Date(jobData.created_at).toLocaleString()}</p>
                <p><strong>Sequence Length:</strong> ${jobData.sequence_length} amino acids</p>
                ${parametersHtml}
            `;
        }

        async function checkJobStatus() {
            if (!currentJobId) return;

            try {
                const response = await fetch(getApiPath(`/blast-job/${currentJobId}`));
                const data = await response.json();

                if (response.ok) {
                    updateStatusDisplay(data);
                    updateJobInfo(data);

                    if (data.status === 'completed') {
                        showResults(data);
                        stopPolling();
                    } else if (data.status === 'failed') {
                        showFailedJob(data);
                        stopPolling();
                    }
                }
            } catch (error) {
                console.error('Error checking job status:', error);
            }
        }

        function updateStatusDisplay(jobData) {
            const statusDisplay = document.getElementById('statusDisplay');
            const statusClass = `status-${jobData.status}`;

            let statusHtml = `
                <span class="badge ${statusClass} status-badge">
                    ${jobData.status.toUpperCase()}
                </span>
            `;

            if (jobData.status === 'running') {
                statusHtml += '<span class="ms-2">Please wait, this may take several minutes...</span>';
            } else if (jobData.status === 'failed') {
                statusHtml += `
                    <div class="alert alert-danger mt-3">
                        <h6><i class="fas fa-exclamation-triangle"></i> Job Failed</h6>
                        <p class="mb-2"><strong>Error:</strong> ${jobData.error || 'Unknown error occurred'}</p>
                        <div class="mt-2">
                            <a href="/blast-search" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> Submit New Search
                            </a>
                            <a href="/blast-job-details?job_id=${jobData.job_id}" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-info-circle"></i> View Details
                            </a>
                        </div>
                    </div>
                `;
            } else if (jobData.status === 'completed') {
                statusHtml += `
                    <div class="mt-2">
                        <a href="/blast-job-details?job_id=${jobData.job_id}" class="btn btn-sm btn-success">
                            <i class="fas fa-eye"></i> View Full Results
                        </a>
                    </div>
                `;
            }

            statusDisplay.innerHTML = statusHtml;
        }

        function showFailedJob(jobData) {
            const resultsSection = document.getElementById('resultsSection');
            const resultsDisplay = document.getElementById('resultsDisplay');

            resultsDisplay.innerHTML = `
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> BLAST Search Failed</h5>
                    <p><strong>Error Details:</strong> ${jobData.error || 'Unknown error occurred during BLAST search'}</p>
                    <hr>
                    <p class="mb-0">
                        <strong>What you can do:</strong>
                    </p>
                    <ul class="mt-2">
                        <li>Check if your protein sequence is valid</li>
                        <li>Try submitting the search again</li>
                        <li>Contact support if the problem persists</li>
                    </ul>
                    <div class="mt-3">
                        <a href="/blast-job-details?job_id=${jobData.job_id}" class="btn btn-outline-danger">
                            <i class="fas fa-info-circle"></i> View Full Error Details
                        </a>
                    </div>
                </div>
            `;

            resultsSection.style.display = 'block';
        }

        function showResults(jobData) {
            const resultsSection = document.getElementById('resultsSection');
            const resultsDisplay = document.getElementById('resultsDisplay');
            
            if (jobData.result && jobData.result.results) {
                const results = jobData.result.results;
                
                if (results.error) {
                    resultsDisplay.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Error parsing results:</strong> ${results.error}
                        </div>
                    `;
                } else if (!results.hits || results.hits.length === 0) {
                    resultsDisplay.innerHTML = `
                        <div class="alert alert-info">
                            <strong>No significant hits found</strong><br>
                            Your sequence did not match any sequences in the database with the current E-value threshold.
                        </div>
                    `;
                } else {
                    let html = `
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>Query Length:</strong> ${results.query_length} amino acids</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Number of Hits:</strong> ${results.hits.length}</p>
                            </div>
                        </div>
                        <h6>Top Hits:</h6>
                    `;
                    
                    results.hits.slice(0, 10).forEach((hit, index) => {
                        const bestHsp = hit.hsps[0];
                        const identity = bestHsp ? Math.round((bestHsp.identity / bestHsp.align_len) * 100) : 0;
                        
                        html += `
                            <div class="hit-card card">
                                <div class="hit-header card-header py-2">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <strong>Hit ${index + 1}:</strong> <code>${hit.id}</code>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <small class="text-muted">Length: ${hit.length}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body py-2">
                                    <p class="mb-2"><strong>Description:</strong> ${hit.description}</p>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <small><strong>E-value:</strong> ${bestHsp ? bestHsp.evalue.toExponential(2) : 'N/A'}</small>
                                        </div>
                                        <div class="col-md-4">
                                            <small><strong>Bit Score:</strong> ${bestHsp ? bestHsp.bit_score.toFixed(1) : 'N/A'}</small>
                                        </div>
                                        <div class="col-md-4">
                                            <small><strong>Identity:</strong> ${identity}%</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    
                    if (results.hits.length > 10) {
                        html += `<p class="text-muted"><em>Showing top 10 hits out of ${results.hits.length} total hits.</em></p>`;
                    }
                    
                    resultsDisplay.innerHTML = html;
                }
            } else {
                resultsDisplay.innerHTML = `
                    <div class="alert alert-warning">
                        No results available
                    </div>
                `;
            }
            
            resultsSection.style.display = 'block';
        }

        function startPolling() {
            pollInterval = setInterval(checkJobStatus, 5000); // Poll every 5 seconds
        }

        function stopPolling() {
            if (pollInterval) {
                clearInterval(pollInterval);
                pollInterval = null;
            }
        }

        // Jobs list functionality
        async function loadJobsList() {
            try {
                const response = await fetch(getApiPath('/blast-jobs'));
                const jobs = await response.json();

                if (response.ok) {
                    displayJobsList(jobs);
                } else {
                    document.getElementById('jobsListContainer').innerHTML = `
                        <div class="alert alert-danger">
                            Failed to load jobs: ${jobs.detail || 'Unknown error'}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('jobsListContainer').innerHTML = `
                    <div class="alert alert-danger">
                        Error loading jobs: ${error.message}
                    </div>
                `;
            }
        }

        function displayJobsList(jobs) {
            const container = document.getElementById('jobsListContainer');

            if (jobs.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>No BLAST jobs found</p>
                        <p class="small">Submit your first BLAST search above to get started</p>
                    </div>
                `;
                return;
            }

            // Sort jobs by creation date (newest first)
            jobs.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Job Name</th>
                                <th>Job ID</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Completed</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            jobs.forEach(job => {
                const statusBadge = getStatusBadge(job.status);
                const createdDate = new Date(job.created_at).toLocaleString();
                const completedDate = job.completed_at ? new Date(job.completed_at).toLocaleString() : '-';
                const shortJobId = job.job_id.substring(0, 8) + '...';

                html += `
                    <tr class="job-row" onclick="viewJobDetails('${job.job_id}')" style="cursor: pointer;">
                        <td>
                            <strong>${job.job_name || 'Unnamed Job'}</strong>
                        </td>
                        <td>
                            <code class="small">${shortJobId}</code>
                        </td>
                        <td>${statusBadge}</td>
                        <td class="small">${createdDate}</td>
                        <td class="small">${completedDate}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
                <p class="text-muted small mt-2">
                    <i class="fas fa-info-circle"></i>
                    Click on any row to view detailed job information and results
                </p>
            `;

            container.innerHTML = html;
        }

        function getStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge bg-warning">Pending</span>',
                'running': '<span class="badge bg-info">Running</span>',
                'completed': '<span class="badge bg-success">Completed</span>',
                'failed': '<span class="badge bg-danger">Failed</span>'
            };
            return badges[status] || `<span class="badge bg-secondary">${status}</span>`;
        }

        function refreshJobsList() {
            document.getElementById('jobsListContainer').innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Refreshing...</span>
                    </div>
                    <p class="mt-2">Refreshing jobs...</p>
                </div>
            `;
            loadJobsList();
        }

        function viewJobDetails(jobId) {
            // Navigate to job details page
            window.location.href = getApiPath(`/blast-job-details?job_id=${jobId}`);
        }

        // Load jobs list when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadJobsList();
        });
    </script>
</body>
</html>
