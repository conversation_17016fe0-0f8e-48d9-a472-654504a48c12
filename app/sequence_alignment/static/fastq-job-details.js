let currentJobId = null;
let originalSequences = null;
let filteredSequences = null;

// Pagination variables
let currentPage = 1;
let sequencesPerPage = 10;
let totalPages = 1;

// Filter and sort elements
let filterSortControls = null;
let resultsCount = null;
let mutationFilter = null;
let insertionFilter = null;
let deletionFilter = null;
let scoreFilter = null;
let sortBy = null;
let applyFilters = null;
let resetFilters = null;
let filteredCount = null;
let totalCount = null;
let sequencesPerPageSelect = null;
let paginationContainer = null;

// Get job ID from URL parameters
function getJobIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('job_id');
}

// Load job details
async function loadJobDetails() {
    currentJobId = getJobIdFromUrl();
    
    if (!currentJobId) {
        showError('No job ID provided in URL');
        return;
    }

    showLoading();

    try {
        const response = await window.pathUtils.fetch(`/fastq-job/${currentJobId}`);
        const jobData = await response.json();

        if (response.ok) {
            displayJobDetails(jobData);

            // Load and display full results if job is completed
            if (jobData.status === 'completed') {
                await loadAndDisplayResults(currentJobId);
                document.getElementById('resultsContainer').classList.remove('d-none');
            } else {
                document.getElementById('noResultsContainer').classList.remove('d-none');
            }
        } else {
            showError(jobData.detail || 'Failed to load job details');
        }
    } catch (error) {
        showError(`Error loading job details: ${error.message}`);
    }
}

function showLoading() {
    document.getElementById('loadingContainer').classList.remove('d-none');
    document.getElementById('errorContainer').classList.add('d-none');
    document.getElementById('jobDetailsContainer').classList.add('d-none');
}

function showError(message) {
    document.getElementById('loadingContainer').classList.add('d-none');
    document.getElementById('errorContainer').classList.remove('d-none');
    document.getElementById('jobDetailsContainer').classList.add('d-none');
    document.getElementById('errorMessage').textContent = message;
}

function displayJobDetails(jobData) {
    console.log('Displaying job details:', jobData);
    document.getElementById('loadingContainer').classList.add('d-none');
    document.getElementById('errorContainer').classList.add('d-none');
    document.getElementById('jobDetailsContainer').classList.remove('d-none');

    // Job information
    document.getElementById('jobName').textContent = jobData.job_name || 'Unnamed Job';
    document.getElementById('jobId').textContent = jobData.job_id;
    document.getElementById('jobStatus').innerHTML = getStatusBadge(jobData.status);
    document.getElementById('jobFilename').textContent = jobData.filename || '-';
    document.getElementById('jobCreated').textContent = new Date(jobData.created_at).toLocaleString();
    document.getElementById('sequenceCount').textContent = jobData.sequence_count || 0;
    
    if (jobData.completed_at) {
        document.getElementById('jobCompleted').textContent = new Date(jobData.completed_at).toLocaleString();
        const duration = Math.round((new Date(jobData.completed_at) - new Date(jobData.created_at)) / 1000);
        document.getElementById('jobDuration').textContent = `${duration} seconds`;
    } else {
        document.getElementById('jobCompleted').textContent = '-';
        document.getElementById('jobDuration').textContent = '-';
    }

    // Reference sequence
    if (jobData.reference_sequence) {
        document.getElementById('referenceSequence').textContent = jobData.reference_sequence;
        document.getElementById('referenceLength').textContent = jobData.reference_length || jobData.reference_sequence.length;
    } else {
        document.getElementById('referenceSequence').textContent = 'Reference sequence not available';
        document.getElementById('referenceLength').textContent = 'Unknown';
    }

    // Alignment parameters
    const params = jobData.parameters || {};
    document.getElementById('matchScore').textContent = params.match_score || '-';
    document.getElementById('mismatchScore').textContent = params.mismatch_score || '-';
    document.getElementById('openGapScore').textContent = params.open_gap_score || '-';
    document.getElementById('extendGapScore').textContent = params.extend_gap_score || '-';
}

// Simplified - no detailed result processing needed

function getStatusBadge(status) {
    const badges = {
        'pending': '<span class="badge bg-warning">Pending</span>',
        'running': '<span class="badge bg-info">Running</span>',
        'completed': '<span class="badge bg-success">Completed</span>',
        'failed': '<span class="badge bg-danger">Failed</span>'
    };
    return badges[status] || `<span class="badge bg-secondary">${status}</span>`;
}

// Download results in JSON format
async function downloadResults() {
    if (!currentJobId) {
        alert('No job ID available');
        return;
    }

    try {
        // Create download URL
        const downloadUrl = window.pathUtils.buildApiUrl(`/fastq-job/${currentJobId}/download?format=json`);

        // Create a temporary link and trigger download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

    } catch (error) {
        console.error('Download error:', error);
        alert(`Failed to download results: ${error.message}`);
    }
}

// Load and display full alignment results
async function loadAndDisplayResults(jobId) {
    try {
        console.log('Loading results for job:', jobId);
        const response = await window.pathUtils.fetch(`/fastq-job/${jobId}/results`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const results = await response.json();
        console.log('Results loaded successfully:', results);

        // Store sequence data in session storage for sequence details page
        if (results && results.sequences) {
            storeSequenceData(results.sequences);
        }

        // Display the results using the same logic as fastq-alignment.js
        displayFullResults(results);

    } catch (error) {
        console.error('Error loading results:', error);

        // Show error message to user
        const errorContainer = document.getElementById('fullResultsSection') || document.createElement('div');
        errorContainer.id = 'fullResultsSection';
        errorContainer.className = 'mt-4';
        errorContainer.innerHTML = `
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Error Loading Results</h5>
                <p>Failed to load alignment results: ${error.message}</p>
                <p class="mb-0">Please try refreshing the page or contact support if the problem persists.</p>
            </div>
        `;

        // Append error container if it's not already in the DOM
        if (!document.getElementById('fullResultsSection')) {
            const jobDetailsContainer = document.getElementById('jobDetailsContainer');
            if (jobDetailsContainer) {
                jobDetailsContainer.appendChild(errorContainer);
            }
        }
    }
}

// Display full results (similar to fastq-alignment.js)
function displayFullResults(data) {
    console.log('Displaying full results:', data);

    try {
        // Extract the actual results from the nested structure
        const results = data.results || data;
        console.log('Extracted results:', results);

        // Validate that we have the required data
        if (!results || typeof results !== 'object') {
            throw new Error('Invalid results data structure');
        }

        const summary = results.summary || {};
        const sequences = results.sequences || [];
        const mutationAnalysis = results.mutation_analysis || null;

        console.log('Summary:', summary);
        console.log('Sequences count:', sequences.length);
        console.log('Mutation analysis:', mutationAnalysis);
        // Create results container if it doesn't exist
        let resultsSection = document.getElementById('fullResultsSection');
        if (!resultsSection) {
            console.log('Creating new results section');
            resultsSection = document.createElement('div');
            resultsSection.id = 'fullResultsSection';
            resultsSection.className = 'mt-4';

            // Insert before the results container (download section)
            const resultsContainer = document.getElementById('resultsContainer');
            const jobDetailsContainer = document.getElementById('jobDetailsContainer');

            console.log('resultsContainer:', resultsContainer);
            console.log('jobDetailsContainer:', jobDetailsContainer);

            if (resultsContainer && resultsContainer.parentNode) {
                console.log('Inserting before resultsContainer');
                resultsContainer.parentNode.insertBefore(resultsSection, resultsContainer);
            } else if (jobDetailsContainer) {
                console.log('Appending to jobDetailsContainer');
                jobDetailsContainer.appendChild(resultsSection);
            } else {
                console.error('Could not find container to insert results');
                return;
            }
        } else {
            console.log('Using existing results section');
        }

        // Display summary
        const summaryHtml = `
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Alignment Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">${summary.total_sequences || 0}</h4>
                            <small>Total Sequences</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">${summary.avg_length || 0}</h4>
                            <small>Avg Sequence Length</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">${summary.avg_alignment_score || 0}</h4>
                            <small>Avg Alignment Score</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">${summary.avg_mutations_per_seq || 0}</h4>
                            <small>Avg Mutations per Seq</small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Display histograms
        const histogramHtml = displayHistograms(sequences);

        // Display mutation analysis
        const mutationAnalysisHtml = displayMutationAnalysis(mutationAnalysis);

        // Store original sequences for filtering/sorting
        originalSequences = [...sequences];
        filteredSequences = [...sequences];

        // Calculate pagination
        totalPages = Math.ceil(filteredSequences.length / sequencesPerPage);

        // Add filter controls and pagination before sequence details
        const filterControlsHtml = `
            <div class="filter-controls mb-3" id="filterSortControls">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-filter"></i> Filter & Sort Sequences</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <label for="mutationFilter" class="form-label">Mutations:</label>
                                <select class="form-select form-select-sm" id="mutationFilter">
                                    <option value="all">All Sequences</option>
                                    <option value="mutated">With Mutations</option>
                                    <option value="no-mutations">No Mutations</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="insertionFilter" class="form-label">Insertions:</label>
                                <select class="form-select form-select-sm" id="insertionFilter">
                                    <option value="all">All</option>
                                    <option value="with-insertions">With Insertions</option>
                                    <option value="no-insertions">No Insertions</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="deletionFilter" class="form-label">Deletions:</label>
                                <select class="form-select form-select-sm" id="deletionFilter">
                                    <option value="all">All</option>
                                    <option value="with-deletions">With Deletions</option>
                                    <option value="no-deletions">No Deletions</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="scoreFilter" class="form-label">Min Score:</label>
                                <input type="number" class="form-control form-control-sm" id="scoreFilter" placeholder="e.g., 1000">
                            </div>
                            <div class="col-md-2">
                                <label for="sortBy" class="form-label">Sort by:</label>
                                <select class="form-select form-select-sm" id="sortBy">
                                    <option value="original">Original Order</option>
                                    <option value="score-desc">Score (High to Low)</option>
                                    <option value="score-asc">Score (Low to High)</option>
                                    <option value="mutations-desc">Mutations (Most to Least)</option>
                                    <option value="mutations-asc">Mutations (Least to Most)</option>
                                    <option value="name-asc">Name (A to Z)</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end gap-2">
                                <button class="btn btn-sm btn-primary" id="applyFilters">
                                    <i class="fas fa-search"></i> Apply
                                </button>
                                <button class="btn btn-sm btn-secondary" id="resetFilters">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination Controls -->
            <div class="d-flex justify-content-between align-items-center mb-3" id="paginationControls">
                <div>
                    <!-- Results count will use the existing element from HTML template -->
                </div>
                <div class="d-flex align-items-center">
                    <label for="sequencesPerPage" class="form-label me-2 mb-0">Show:</label>
                    <select class="form-select form-select-sm me-3" id="sequencesPerPage" style="width: auto;">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="all">All</option>
                    </select>
                    <div id="paginationContainer"></div>
                </div>
            </div>
        `;

        const sequenceDetailsHtml = `
            <div class="card" id="sequenceDetailsCard">
                <div class="card-header">
                    <h5><i class="fas fa-dna"></i> Sequence Details</h5>
                </div>
                <div class="card-body" id="sequenceDetailsBody">
                    <!-- Sequences will be populated by displayCurrentPage() -->
                </div>
            </div>
        `;

        // Remove any existing filter controls to avoid duplicates
        const existingFilterControls = document.getElementById('filterSortControls');
        if (existingFilterControls) {
            console.log('Removing existing filter controls to avoid duplicates');
            existingFilterControls.remove();
        }

        resultsSection.innerHTML = summaryHtml + histogramHtml + mutationAnalysisHtml + filterControlsHtml + sequenceDetailsHtml;
        console.log('Results displayed successfully');

        // Initialize filter elements after DOM update with a small delay
        setTimeout(() => {
            initializeFilterElements();

            // Update counts
            updateResultsCount();

            // Display current page of sequences
            displayCurrentPage();
        }, 100);

        // Render histograms after DOM is updated
        setTimeout(() => {
            if (window.histogramData) {
                // console.log('Rendering histograms with data:', window.histogramData);
                renderHistogram('alignmentScoreHistogram', window.histogramData.alignmentScores, 'Alignment Score', '#007bff');
                renderHistogram('avgQualityHistogram', window.histogramData.avgQualities, 'Average Quality', '#28a745');
                renderHistogram('sequenceLengthHistogram', window.histogramData.sequenceLengths, 'Length (bp)', '#17a2b8');
                renderHistogram('mutationCountHistogram', window.histogramData.mutationCounts, 'Mutations', '#dc3545');
                renderHistogram('gcContentHistogram', window.histogramData.gcContents, 'GC Content (%)', '#ffc107');
                renderHistogram('identityPercentHistogram', window.histogramData.identityPercents, 'Identity (%)', '#6f42c1');
            } else {
                console.error('No histogram data available');
            }
        }, 200);

    } catch (error) {
        console.error('Error displaying results:', error);

        // Show error message
        const errorHtml = `
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Display Error</h5>
                <p>Failed to display results: ${error.message}</p>
            </div>
        `;

        const resultsSection = document.getElementById('fullResultsSection');
        if (resultsSection) {
            resultsSection.innerHTML = errorHtml;
        }
    }
}

// Display histograms for various metrics
function displayHistograms(sequences) {
    if (!sequences || sequences.length === 0) {
        return '<div class="alert alert-info">No sequence data available for histograms</div>';
    }

    // Extract data for histograms
    const alignmentScores = sequences.map(seq => seq.alignment_score || 0);
    const avgQualities = sequences.map(seq => seq.avg_quality || 0);
    const sequenceLengths = sequences.map(seq => seq.length || 0);
    const mutationCounts = sequences.map(seq => seq.num_mutations || 0);
    const gcContents = sequences.map(seq => seq.gc_content || 0);
    const identityPercents = sequences.map(seq => seq.identity_percent || 0);

    // Store data for rendering after DOM update
    window.histogramData = {
        alignmentScores,
        avgQualities,
        sequenceLengths,
        mutationCounts,
        gcContents,
        identityPercents
    };

    return `
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> Data Distribution Histograms</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <h6>Alignment Scores</h6>
                        <div id="alignmentScoreHistogram" class="histogram-container"></div>
                        <small class="text-muted">Distribution of alignment scores across all sequences</small>
                    </div>
                    <div class="col-md-6 mb-4">
                        <h6>Average Quality Scores</h6>
                        <div id="avgQualityHistogram" class="histogram-container"></div>
                        <small class="text-muted">Distribution of average quality scores</small>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <h6>Sequence Lengths</h6>
                        <div id="sequenceLengthHistogram" class="histogram-container"></div>
                        <small class="text-muted">Distribution of sequence lengths (base pairs)</small>
                    </div>
                    <div class="col-md-6 mb-4">
                        <h6>Mutation Counts</h6>
                        <div id="mutationCountHistogram" class="histogram-container"></div>
                        <small class="text-muted">Distribution of mutation counts per sequence</small>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <h6>GC Content (%)</h6>
                        <div id="gcContentHistogram" class="histogram-container"></div>
                        <small class="text-muted">Distribution of GC content percentages</small>
                    </div>
                    <div class="col-md-6 mb-4">
                        <h6>Identity Percentage</h6>
                        <div id="identityPercentHistogram" class="histogram-container"></div>
                        <small class="text-muted">Distribution of sequence identity percentages</small>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Function to render a histogram using simple HTML/CSS bars
function renderHistogram(containerId, data, _label, color) {
    // console.log(`Rendering histogram for ${containerId} with ${data?.length || 0} data points`);

    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Container ${containerId} not found`);
        return;
    }

    if (!data || data.length === 0) {
        console.warn(`No data available for ${containerId}`);
        container.innerHTML = '<div class="text-muted">No data available</div>';
        return;
    }

    // Calculate histogram bins
    const min = Math.min(...data);
    const max = Math.max(...data);
    const range = max - min;
    const binCount = Math.min(20, Math.max(5, Math.ceil(Math.sqrt(data.length))));
    const binSize = range / binCount;

    // Create bins
    const bins = new Array(binCount).fill(0);
    const binLabels = [];

    for (let i = 0; i < binCount; i++) {
        const binStart = min + (i * binSize);
        const binEnd = min + ((i + 1) * binSize);
        binLabels.push(`${binStart.toFixed(1)}-${binEnd.toFixed(1)}`);
    }

    // Fill bins
    data.forEach(value => {
        let binIndex = Math.floor((value - min) / binSize);
        if (binIndex >= binCount) binIndex = binCount - 1;
        if (binIndex < 0) binIndex = 0;
        bins[binIndex]++;
    });

    // Find max count for scaling
    const maxCount = Math.max(...bins);

    // Generate HTML
    let html = '<div class="histogram">';

    bins.forEach((count, index) => {
        const height = maxCount > 0 ? (count / maxCount) * 100 : 0;
        const percentage = data.length > 0 ? ((count / data.length) * 100).toFixed(1) : 0;

        html += `
            <div class="histogram-bar-container" title="${binLabels[index]}: ${count} sequences (${percentage}%)">
                <div class="histogram-bar" style="height: ${height}%; background-color: ${color};">
                    <span class="histogram-count">${count}</span>
                </div>
                <div class="histogram-label">${binLabels[index]}</div>
            </div>
        `;
    });

    html += '</div>';

    // Add statistics
    const mean = (data.reduce((a, b) => a + b, 0) / data.length).toFixed(2);
    const median = [...data].sort((a, b) => a - b)[Math.floor(data.length / 2)].toFixed(2);

    container.innerHTML = html;

    // Add statistics box outside the histogram container
    const statsContainer = document.createElement('div');
    statsContainer.className = 'histogram-stats mt-2';
    statsContainer.innerHTML = `
        <small class="text-muted">
            Mean: ${mean} | Median: ${median} | Min: ${min.toFixed(2)} | Max: ${max.toFixed(2)} | Count: ${data.length}
        </small>
    `;

    // Insert stats after the histogram container
    container.parentNode.insertBefore(statsContainer, container.nextSibling);
    // console.log(`Successfully rendered histogram for ${containerId}`);
}

// Display mutation analysis (simplified version)
function displayMutationAnalysis(mutationAnalysis) {
    if (!mutationAnalysis) {
        return '<div class="alert alert-info">No mutation analysis data available</div>';
    }

    const stats = mutationAnalysis.statistics;
    const topMutations = mutationAnalysis.top_mutations || [];
    const topPatterns = mutationAnalysis.top_patterns || [];

    let html = `
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-dna"></i> Mutation Analysis</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title text-primary">${stats.total_mutations}</h5>
                                <p class="card-text">Total Mutations</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title text-warning">${stats.mutation_rate}%</h5>
                                <p class="card-text">Sequences with Mutations</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title text-info">${stats.avg_mutations_per_sequence}</h5>
                                <p class="card-text">Avg Mutations per Sequence</p>
                            </div>
                        </div>
                    </div>
                </div>
    `;

    if (topMutations.length > 0) {
        html += `
            <h6><i class="fas fa-trophy"></i> Top Mutations</h6>
            <div class="table-responsive mb-4">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Mutation</th>
                            <th>Count</th>
                            <th>Frequency</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${topMutations.slice(0, 5).map(mutation => `
                            <tr>
                                <td><span class="badge bg-secondary">${mutation.rank}</span></td>
                                <td><code>${mutation.mutation}</code></td>
                                <td><strong>${mutation.count}</strong></td>
                                <td>${mutation.percentage}%</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    if (topPatterns.length > 0) {
        html += `
            <h6><i class="fas fa-layer-group"></i> Top Mutation Patterns</h6>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Pattern</th>
                            <th>Count</th>
                            <th>Examples</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${topPatterns.slice(0, 5).map(pattern => `
                            <tr>
                                <td><span class="badge bg-secondary">${pattern.rank}</span></td>
                                <td>
                                    ${pattern.mutations.map(mut =>
                                        `<span class="badge bg-danger me-1">${mut}</span>`
                                    ).join('')}
                                </td>
                                <td><strong>${pattern.count}</strong></td>
                                <td>
                                    ${pattern.example_sequences.map(seq =>
                                        `<a href="sequence-details?id=${encodeURIComponent(seq.id)}" class="text-decoration-none">
                                            <code>${seq.id}</code>
                                        </a>`
                                    ).join(', ')}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    html += `
            </div>
        </div>
    `;

    return html;
}

// Initialize filter elements after DOM is created
function initializeFilterElements() {
    console.log('Initializing filter elements...');

    filterSortControls = document.getElementById('filterSortControls');
    resultsCount = document.getElementById('resultsCount');
    mutationFilter = document.getElementById('mutationFilter');
    insertionFilter = document.getElementById('insertionFilter');
    deletionFilter = document.getElementById('deletionFilter');
    scoreFilter = document.getElementById('scoreFilter');
    sortBy = document.getElementById('sortBy');
    applyFilters = document.getElementById('applyFilters');
    resetFilters = document.getElementById('resetFilters');
    filteredCount = document.getElementById('filteredCount');
    totalCount = document.getElementById('totalCount');
    sequencesPerPageSelect = document.getElementById('sequencesPerPage');
    paginationContainer = document.getElementById('paginationContainer');

    // console.log('Filter elements found:', {
    //     mutationFilter: !!mutationFilter,
    //     insertionFilter: !!insertionFilter,
    //     deletionFilter: !!deletionFilter,
    //     scoreFilter: !!scoreFilter,
    //     sortBy: !!sortBy,
    //     applyFilters: !!applyFilters,
    //     resetFilters: !!resetFilters,
    //     sequencesPerPageSelect: !!sequencesPerPageSelect
    // });

    // Add event listeners - only for apply button and sequences per page
    if (applyFilters) {
        applyFilters.addEventListener('click', function() {
            console.log('Apply button clicked!');
            applyFiltersAndSort();
        });
        // console.log('Added apply filters listener');

        // Test if button is clickable
        console.log('Apply button element:', applyFilters);
    } else {
        console.error('Apply button not found!');
    }
    if (resetFilters) {
        resetFilters.addEventListener('click', resetAllFilters);
        console.log('Added reset filters listener');
    }
    if (sequencesPerPageSelect) {
        sequencesPerPageSelect.addEventListener('change', changeSequencesPerPage);
        console.log('Added sequences per page listener');
    }

    // Add Enter key support for score filter
    if (scoreFilter) {
        scoreFilter.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFiltersAndSort();
            }
        });
        console.log('Added score filter Enter key listener');
    }

    console.log('Filter elements initialization complete');
}

// Update results count display
function updateResultsCount() {
    // Use the existing elements from HTML template
    const totalCountElement = document.getElementById('totalCount');
    const filteredCountElement = document.getElementById('filteredCount');
    const resultsCountElement = document.getElementById('resultsCount');

    if (totalCountElement) totalCountElement.textContent = originalSequences.length;
    if (filteredCountElement) filteredCountElement.textContent = filteredSequences.length;

    // Show the results count element
    if (resultsCountElement) {
        resultsCountElement.style.display = 'block';
    }
}

// Display current page of sequences
function displayCurrentPage() {
    console.log('filteredSequences length:', filteredSequences ? filteredSequences.length : 'null');

    const sequenceDetailsBody = document.getElementById('sequenceDetailsBody');
    if (!sequenceDetailsBody) {
        console.error('Could not find sequence details body');
        return;
    }

    // Calculate start and end indices for current page
    let startIndex, endIndex;
    if (sequencesPerPage === 'all') {
        startIndex = 0;
        endIndex = filteredSequences.length;
    } else {
        startIndex = (currentPage - 1) * sequencesPerPage;
        endIndex = Math.min(startIndex + sequencesPerPage, filteredSequences.length);
    }

    console.log('Display range:', startIndex, 'to', endIndex, 'of', filteredSequences.length);
    const sequencesToShow = filteredSequences.slice(startIndex, endIndex);
    console.log('Sequences to show:', sequencesToShow.length);

    const sequenceDetailsHtml = `
        ${sequencesToShow.map(seq => `
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-dna"></i>
                        <a href="javascript:void(0)" onclick="openSequenceDetails('${seq.id.replace(/'/g, "\\'")}')" class="sequence-link text-decoration-none">
                            ${seq.id}
                        </a>
                        ${seq.is_reverse_complement ? '<span class="badge bg-warning">Reverse Complement</span>' : ''}
                        ${seq.insertion ? '<span class="badge bg-info">Insertion</span>' : ''}
                        ${seq.deletion ? '<span class="badge bg-danger">Deletion</span>' : ''}
                    </h6>
                    <span class="badge bg-primary">Score: ${seq.alignment_score}</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Length:</strong> ${seq.length} bp<br>
                                <strong>Avg Quality:</strong> ${seq.avg_quality}<br>
                                <strong>Identity:</strong> ${seq.identity_percent}%
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Mutations:</strong> ${seq.num_mutations}<br>
                                <strong>GC Content:</strong> ${seq.gc_content}%<br>
                                <strong>Min/Max Quality:</strong> ${seq.min_quality}/${seq.max_quality}
                            </small>
                        </div>
                    </div>
                    ${seq.mutations && seq.mutations.length > 0 ? `
                        <div class="mt-2">
                            <strong>Mutations:</strong><br>
                            ${seq.mutations.slice(0, 30).map(mut =>
                                `<span class="badge bg-secondary me-1">${mut.mutation}</span>`
                            ).join('')}
                            ${seq.num_mutations > 30 ? `<span class="text-muted">... and ${seq.num_mutations - 30} more</span>` : ''}
                        </div>
                    ` : '<div class="mt-2 text-success"><i class="fas fa-check"></i> No mutations found</div>'}
                </div>
            </div>
        `).join('')}
        ${filteredSequences.length === 0 ? `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                No sequences match the current filters.
            </div>
        ` : ''}
    `;

    console.log('About to update DOM with HTML length:', sequenceDetailsHtml.length);
    console.log('sequenceDetailsBody element:', sequenceDetailsBody);

    sequenceDetailsBody.innerHTML = sequenceDetailsHtml;

    // Update pagination
    updatePagination();
}

// Update pagination controls
function updatePagination() {
    if (!paginationContainer) return;

    if (sequencesPerPage === 'all') {
        paginationContainer.innerHTML = '';
        return;
    }

    totalPages = Math.ceil(filteredSequences.length / sequencesPerPage);

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    // Ensure current page is within bounds
    if (currentPage > totalPages) currentPage = totalPages;
    if (currentPage < 1) currentPage = 1;

    let paginationHtml = '<nav aria-label="Sequence pagination"><ul class="pagination pagination-sm mb-0">';

    // Previous button
    paginationHtml += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToPage(${currentPage - 1}); return false;">Previous</a>
        </li>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        paginationHtml += '<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1); return false;">1</a></li>';
        if (startPage > 2) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="goToPage(${i}); return false;">${i}</a>
            </li>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // Next button
    paginationHtml += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToPage(${currentPage + 1}); return false;">Next</a>
        </li>
    `;

    paginationHtml += '</ul></nav>';
    paginationContainer.innerHTML = paginationHtml;
}

// Go to specific page
function goToPage(page) {
    if (page < 1 || page > totalPages) return;
    currentPage = page;
    displayCurrentPage();
}

// Store sequence data in session storage
function storeSequenceData(sequences) {
    try {
        // Create a map of sequence ID to sequence data for easy lookup
        const sequenceMap = {};
        sequences.forEach(seq => {
            sequenceMap[seq.id] = seq;
        });

        // Store in session storage (will persist until browser tab is closed)
        sessionStorage.setItem('sequenceData', JSON.stringify(sequenceMap));
        console.log('Stored sequence data for', sequences.length, 'sequences');
    } catch (error) {
        console.error('Failed to store sequence data:', error);
    }
}

// Open sequence details page
function openSequenceDetails(sequenceId) {
    // Properly encode the sequence ID for URL
    const encodedId = encodeURIComponent(sequenceId);
    const url = window.pathUtils.buildUrl(`sequence-details?id=${encodedId}`);
    window.open(url, '_blank');
}

// Change sequences per page
function changeSequencesPerPage() {
    const sequencesPerPageElement = document.getElementById('sequencesPerPage');
    const newValue = sequencesPerPageElement ? sequencesPerPageElement.value : '10';
    sequencesPerPage = newValue === 'all' ? 'all' : parseInt(newValue);
    currentPage = 1; // Reset to first page
    displayCurrentPage();
}

// Apply filters and sorting to sequences
function applyFiltersAndSort() {
    console.log('applyFiltersAndSort called');

    if (!originalSequences) {
        console.log('No original sequences available');
        return;
    }

    console.log('Original sequences count:', originalSequences.length);

    // Debug: Show sample sequence structure
    if (originalSequences.length > 0) {
        console.log('Sample sequence structure:', originalSequences[0]);
        console.log('Sample sequence properties:', Object.keys(originalSequences[0]));

        // Check mutation counts in first few sequences
        const sampleMutations = originalSequences.slice(0, 5).map(seq => ({
            id: seq.id,
            num_mutations: seq.num_mutations,
            insertion: seq.insertion,
            deletion: seq.deletion,
            alignment_score: seq.alignment_score
        }));
        console.log('Sample mutation data:', sampleMutations);
    }

    // Show loading state on apply button
    if (applyFilters) {
        applyFilters.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Applying...';
        applyFilters.disabled = true;
    }

    // Start with original sequences
    let filtered = [...originalSequences];

    // Read filter values directly from DOM (in case element references are stale)
    const mutationFilterElement = document.getElementById('mutationFilter');
    const insertionFilterElement = document.getElementById('insertionFilter');
    const deletionFilterElement = document.getElementById('deletionFilter');
    const scoreFilterElement = document.getElementById('scoreFilter');
    const sortByElement = document.getElementById('sortBy');

    // Debug: Check if elements exist
    console.log('Filter elements found:', {
        mutationFilter: !!mutationFilterElement,
        insertionFilter: !!insertionFilterElement,
        deletionFilter: !!deletionFilterElement,
        scoreFilter: !!scoreFilterElement,
        sortBy: !!sortByElement
    });

    if (!mutationFilterElement) {
        console.error('mutationFilter element not found! Checking all select elements...');
        const allSelects = document.querySelectorAll('select');
        console.log('All select elements:', allSelects);
        allSelects.forEach((select, index) => {
            console.log(`Select ${index}:`, select.id, select.className, select);
        });
    }

    // Apply mutation filter
    const mutationFilterValue = mutationFilterElement ? mutationFilterElement.value : 'all';
    console.log('Mutation filter value:', mutationFilterValue);

    if (mutationFilterValue === 'mutated') {
        const beforeCount = filtered.length;
        filtered = filtered.filter(seq => seq.num_mutations > 0);
        console.log(`Mutation filter: ${beforeCount} -> ${filtered.length} sequences`);
    } else if (mutationFilterValue === 'no-mutations') {
        const beforeCount = filtered.length;
        filtered = filtered.filter(seq => seq.num_mutations === 0);
        console.log(`No mutation filter: ${beforeCount} -> ${filtered.length} sequences`);
    }

    // Apply insertion filter
    const insertionFilterValue = insertionFilterElement ? insertionFilterElement.value : 'all';
    console.log('Insertion filter value:', insertionFilterValue);

    if (insertionFilterValue === 'with-insertions') {
        const beforeCount = filtered.length;
        filtered = filtered.filter(seq => seq.insertion === true);
        console.log(`Insertion filter: ${beforeCount} -> ${filtered.length} sequences`);
    } else if (insertionFilterValue === 'no-insertions') {
        const beforeCount = filtered.length;
        filtered = filtered.filter(seq => seq.insertion !== true);
        console.log(`No insertion filter: ${beforeCount} -> ${filtered.length} sequences`);
    }

    // Apply deletion filter
    const deletionFilterValue = deletionFilterElement ? deletionFilterElement.value : 'all';
    console.log('Deletion filter value:', deletionFilterValue);

    if (deletionFilterValue === 'with-deletions') {
        const beforeCount = filtered.length;
        filtered = filtered.filter(seq => seq.deletion === true);
        console.log(`Deletion filter: ${beforeCount} -> ${filtered.length} sequences`);
    } else if (deletionFilterValue === 'no-deletions') {
        const beforeCount = filtered.length;
        filtered = filtered.filter(seq => seq.deletion !== true);
        console.log(`No deletion filter: ${beforeCount} -> ${filtered.length} sequences`);
    }

    // Apply score filter
    const minScore = parseFloat(scoreFilterElement ? scoreFilterElement.value : '');
    console.log('Score filter value:', scoreFilterElement ? scoreFilterElement.value : 'null', 'parsed:', minScore);

    if (!isNaN(minScore)) {
        const beforeCount = filtered.length;
        filtered = filtered.filter(seq => seq.alignment_score >= minScore);
        console.log(`Score filter (>= ${minScore}): ${beforeCount} -> ${filtered.length} sequences`);
    }

    // Apply sorting
    const sortValue = sortByElement ? sortByElement.value : 'original';
    console.log('Sort value:', sortValue);

    switch (sortValue) {
        case 'score-desc':
            filtered.sort((a, b) => b.alignment_score - a.alignment_score);
            console.log('Sorted by score (desc)');
            break;
        case 'score-asc':
            filtered.sort((a, b) => a.alignment_score - b.alignment_score);
            console.log('Sorted by score (asc)');
            break;
        case 'mutations-desc':
            filtered.sort((a, b) => b.num_mutations - a.num_mutations);
            console.log('Sorted by mutations (desc)');
            break;
        case 'mutations-asc':
            filtered.sort((a, b) => a.num_mutations - b.num_mutations);
            console.log('Sorted by mutations (asc)');
            break;
        case 'name-asc':
            filtered.sort((a, b) => a.id.localeCompare(b.id));
            console.log('Sorted by name (asc)');
            break;
        case 'original':
        default:
            console.log('Keeping original order');
            break;
    }

    // Update filtered sequences
    filteredSequences = filtered;

    // Reset to first page when filters change
    currentPage = 1;

    // Update count display
    updateResultsCount();

    // Re-display current page with filtered data
    console.log('About to call displayCurrentPage with', filteredSequences.length, 'filtered sequences');
    displayCurrentPage();

    // Restore apply button state
    if (applyFilters) {
        applyFilters.innerHTML = '<i class="fas fa-search"></i> Apply';
        applyFilters.disabled = false;
    }

    console.log('Filters applied successfully. Filtered count:', filteredSequences.length);
    console.log('Current page:', currentPage, 'Sequences per page:', sequencesPerPage);
}

function resetAllFilters() {
    console.log('Resetting all filters');

    // Reset all filter controls using DOM elements directly
    const mutationFilterElement = document.getElementById('mutationFilter');
    const insertionFilterElement = document.getElementById('insertionFilter');
    const deletionFilterElement = document.getElementById('deletionFilter');
    const scoreFilterElement = document.getElementById('scoreFilter');
    const sortByElement = document.getElementById('sortBy');
    const sequencesPerPageElement = document.getElementById('sequencesPerPage');

    if (mutationFilterElement) mutationFilterElement.value = 'all';
    if (insertionFilterElement) insertionFilterElement.value = 'all';
    if (deletionFilterElement) deletionFilterElement.value = 'all';
    if (scoreFilterElement) scoreFilterElement.value = '';
    if (sortByElement) sortByElement.value = 'original';
    if (sequencesPerPageElement) sequencesPerPageElement.value = '10';

    // Reset pagination
    sequencesPerPage = 10;
    currentPage = 1;

    // Apply filters (which will reset to show all)
    applyFiltersAndSort();
}

// Test function for debugging (can be called from browser console)
window.testApplyFilters = function() {
    console.log('Testing apply filters...');
    console.log('originalSequences:', originalSequences ? originalSequences.length : 'null');
    console.log('filteredSequences:', filteredSequences ? filteredSequences.length : 'null');
    console.log('Filter elements:', {
        mutationFilter: mutationFilter ? mutationFilter.value : 'null',
        insertionFilter: insertionFilter ? insertionFilter.value : 'null',
        deletionFilter: deletionFilter ? deletionFilter.value : 'null',
        scoreFilter: scoreFilter ? scoreFilter.value : 'null',
        sortBy: sortBy ? sortBy.value : 'null'
    });

    if (typeof applyFiltersAndSort === 'function') {
        applyFiltersAndSort();
    } else {
        console.error('applyFiltersAndSort function not found');
    }
};

// Test function to manually filter sequences (for debugging)
window.testManualFilter = function() {
    if (!originalSequences) {
        console.log('No sequences available');
        return;
    }

    console.log('Testing manual filter...');
    console.log('Total sequences:', originalSequences.length);

    // Test simple filter - sequences with score > 1000
    const highScoreSeqs = originalSequences.filter(seq => seq.alignment_score > 1000);
    console.log('Sequences with score > 1000:', highScoreSeqs.length);

    // Test mutation filter
    const mutatedSeqs = originalSequences.filter(seq => seq.num_mutations > 0);
    console.log('Sequences with mutations > 0:', mutatedSeqs.length);

    const noMutationSeqs = originalSequences.filter(seq => seq.num_mutations === 0);
    console.log('Sequences with mutations = 0:', noMutationSeqs.length);

    // Show some sample data
    if (originalSequences.length > 0) {
        console.log('Sample sequence:', originalSequences[0]);
    }
};

// Test function to manually update display with filtered sequences
window.testDisplayUpdate = function() {
    if (!originalSequences) {
        console.log('No sequences available');
        return;
    }

    console.log('Testing display update...');

    // Manually filter to sequences with mutations
    const mutatedSeqs = originalSequences.filter(seq => seq.num_mutations > 0);
    console.log('Manually filtered to', mutatedSeqs.length, 'sequences with mutations');

    // Update filteredSequences
    filteredSequences = mutatedSeqs;

    // Update counts
    updateResultsCount();

    // Update display
    displayCurrentPage();

    console.log('Manual display update complete');
};

// Test function to check current filter values
window.checkFilterValues = function() {
    console.log('Current filter values:');
    console.log('mutationFilter element:', mutationFilter);
    console.log('mutationFilter value:', mutationFilter ? mutationFilter.value : 'null');
    console.log('insertionFilter value:', insertionFilter ? insertionFilter.value : 'null');
    console.log('deletionFilter value:', deletionFilter ? deletionFilter.value : 'null');
    console.log('scoreFilter value:', scoreFilter ? scoreFilter.value : 'null');
    console.log('sortBy value:', sortBy ? sortBy.value : 'null');

    // Check if elements are actually in the DOM
    const mutationFilterDOM = document.getElementById('mutationFilter');
    const insertionFilterDOM = document.getElementById('insertionFilter');
    const deletionFilterDOM = document.getElementById('deletionFilter');

    console.log('DOM elements:');
    console.log('mutationFilter DOM:', mutationFilterDOM);
    console.log('mutationFilter DOM value:', mutationFilterDOM ? mutationFilterDOM.value : 'null');
    console.log('insertionFilter DOM value:', insertionFilterDOM ? insertionFilterDOM.value : 'null');
    console.log('deletionFilter DOM value:', deletionFilterDOM ? deletionFilterDOM.value : 'null');
};

// Debug function to check what's in the DOM
window.debugDOM = function() {
    console.log('=== DOM DEBUG ===');

    // Check if filter controls section exists
    const filterControls = document.getElementById('filterSortControls');
    console.log('filterSortControls element:', filterControls);

    // Check all elements with filter-related IDs
    const ids = ['mutationFilter', 'insertionFilter', 'deletionFilter', 'scoreFilter', 'sortBy', 'applyFilters', 'resetFilters'];
    ids.forEach(id => {
        const element = document.getElementById(id);
        console.log(`${id}:`, element);
        if (element && element.tagName === 'SELECT') {
            console.log(`  ${id} value:`, element.value);
            console.log(`  ${id} options:`, Array.from(element.options).map(opt => opt.value));
        }
    });

    // Check if the full results section exists
    const fullResults = document.getElementById('fullResultsSection');
    console.log('fullResultsSection:', fullResults);

    // List all elements with IDs in the page
    const allElementsWithIds = document.querySelectorAll('[id]');
    console.log('All elements with IDs:', Array.from(allElementsWithIds).map(el => el.id));

    // Check for duplicate IDs
    const idCounts = {};
    Array.from(allElementsWithIds).forEach(el => {
        idCounts[el.id] = (idCounts[el.id] || 0) + 1;
    });

    const duplicateIds = Object.entries(idCounts).filter(([_id, count]) => count > 1);
    if (duplicateIds.length > 0) {
        console.warn('Duplicate IDs found:', duplicateIds);
    } else {
        console.log('No duplicate IDs found');
    }
};

// Load job details when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadJobDetails();
});
