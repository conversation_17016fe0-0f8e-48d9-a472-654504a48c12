/* Sequence Details Page Styles */

.sequence-display {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    word-break: break-all;
    white-space: pre-wrap;
}

.sequence-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.info-card {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin-bottom: 15px;
}

.mutation-highlight {
    background-color: #ffebee;
    color: #c62828;
    font-weight: bold;
    padding: 1px 2px;
    border-radius: 2px;
}

.quality-bar {
    height: 20px;
    background: linear-gradient(to right, #dc3545, #ffc107, #28a745);
    border-radius: 10px;
    position: relative;
}

.quality-indicator {
    position: absolute;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: #000;
    border-radius: 1px;
}

.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
}

.loading {
    display: none;
    text-align: center;
    padding: 50px;
}

.error-message {
    display: none;
}

.alignment-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    overflow-x: auto;
}

.alignment-sequence {
    margin-bottom: 5px;
}

.alignment-text {
    background: transparent;
    color: #333;
    font-size: 12px;
    line-height: 1.2;
    word-break: break-all;
    white-space: pre;
    margin: 0;
    padding: 0;
}

.alignment-text-container {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 3px;
    padding: 8px;
    margin-bottom: 8px;
    overflow-x: auto;
}

.highlight-mutation {
    background-color: #ffebee;
    color: #c62828;
    font-weight: bold;
    padding: 1px 2px;
    border-radius: 2px;
}

.highlight-deletion {
    background-color: #fff3e0;
    color: #ef6c00;
    font-weight: bold;
    padding: 1px 2px;
    border-radius: 2px;
}

.highlight-insertion {
    background-color: #e8f5e8;
    color: #2e7d32;
    font-weight: bold;
    padding: 1px 2px;
    border-radius: 2px;
}

.alignment-legend {
    border-top: 1px solid #dee2e6;
    padding-top: 10px;
}

.legend-item {
    display: inline-block;
    vertical-align: middle;
}

/* Navigation tabs */
.nav-tabs .nav-link {
    color: #495057;
    padding: 12px 20px;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    font-weight: 600;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.nav-tabs .nav-link:hover {
    color: #007bff;
    background-color: #f8f9fa;
}

.nav-justified .nav-item {
    flex-basis: 0;
    flex-grow: 1;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    background-color: #fff;
}

/* Info cards styling */
.info-card h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 10px;
}

.info-card p {
    margin-bottom: 5px;
    font-size: 0.95em;
}

.info-card p:last-child {
    margin-bottom: 0;
}

/* Badge styles */
.badge {
    font-size: 0.75em;
    margin-right: 5px;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Button styles */
.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

/* Quality visualization */
.quality-summary {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.quality-summary p {
    margin-bottom: 8px;
}

.quality-summary p:last-of-type {
    margin-bottom: 15px;
}

/* Sequence header badges */
#sequenceBadges .badge {
    margin-left: 5px;
    font-size: 0.8em;
}

/* Responsive design */
@media (max-width: 768px) {
    .sequence-header {
        padding: 15px;
    }
    
    .sequence-header h2 {
        font-size: 1.5em;
    }
    
    .back-button {
        position: relative;
        top: auto;
        left: auto;
        margin-bottom: 15px;
    }
    
    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.9em;
    }
    
    .info-card {
        margin-bottom: 10px;
        padding: 10px;
    }
    
    .sequence-display {
        font-size: 12px;
        padding: 10px;
    }
}

/* Loading spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Error message styling */
.alert-warning {
    border-left: 4px solid #ffc107;
}

.alert-warning h5 {
    color: #856404;
}

/* Mutation badges in details */
.mutation-badge {
    margin-right: 3px;
    margin-bottom: 3px;
}

/* Copy and download buttons */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Alignment display improvements */
.alignment-container {
    max-height: 400px;
    overflow-y: auto;
}

/* Tab content padding */
.tab-pane {
    padding: 15px;
}

/* Statistics display */
.text-center h6 {
    color: #495057;
    font-weight: 600;
}

/* Container margins */
.container {
    max-width: 1200px;
}

/* Card styling */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Text utilities */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-muted {
    color: #6c757d !important;
}
