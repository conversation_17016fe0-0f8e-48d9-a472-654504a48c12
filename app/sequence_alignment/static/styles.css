/* Main Application Styles */

.upload-area {
    border: 2px dashed #007bff;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.upload-area:hover {
    border-color: #0056b3;
    background-color: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background-color: #d4edda;
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.alignment-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    overflow-x: auto;
}

.alignment-sequence {
    margin-bottom: 5px;
}

.alignment-text {
    background: transparent;
    color: #333;
    font-size: 12px;
    line-height: 1.2;
    word-break: break-all;
}

.mutation-highlight {
    font-weight: bold;
    padding: 1px 2px;
    border-radius: 2px;
}

.ref-mutation {
    background-color: #ffebee;
    color: #c62828;
}

.query-mutation {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.filter-controls {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}

.form-label {
    font-weight: 600;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.form-select-sm, .form-control-sm {
    font-size: 0.875rem;
}

.mutation-code {
    background-color: #f8f9fa;
    color: #e83e8c;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

.nav-tabs .nav-link {
    color: #495057;
    padding: 12px 20px;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    font-weight: 600;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.nav-tabs .nav-link:hover {
    color: #007bff;
    background-color: #f8f9fa;
}

.nav-justified .nav-item {
    flex-basis: 0;
    flex-grow: 1;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    background-color: #fff;
}

.progress {
    background-color: #e9ecef;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.sequence-id {
    background-color: #e9ecef;
    color: #495057;
    font-size: 0.8em;
    padding: 2px 4px;
    border-radius: 2px;
    margin-right: 4px;
}

.sequence-link {
    color: #007bff;
    font-weight: 600;
    transition: color 0.2s ease;
}

.sequence-link:hover {
    color: #0056b3;
    text-decoration: underline !important;
}

/* Loading and utility styles */
.loading {
    display: none;
}

.mutation-badge {
    margin-right: 2px;
    margin-bottom: 2px;
}

.sequence-card {
    margin-bottom: 15px;
}

.sequence-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.sequence-card .card-body {
    padding: 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .upload-area {
        padding: 20px;
    }
    
    .filter-controls .row > div {
        margin-bottom: 10px;
    }
    
    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.9em;
    }
}

/* Chart container styles */
.chart-container {
    position: relative;
    height: 250px;
    margin-bottom: 20px;
}

/* Distribution charts specific styles */
#distributionCharts {
    margin-top: 15px;
}

#distributionCharts .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

#distributionCharts h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Alignment highlighting styles */
.highlight-mutation {
    background-color: #ffebee;
    color: #c62828;
    font-weight: bold;
    padding: 1px 2px;
    border-radius: 2px;
}

.highlight-deletion {
    background-color: #fff3e0;
    color: #ef6c00;
    font-weight: bold;
    padding: 1px 2px;
    border-radius: 2px;
}

.highlight-insertion {
    background-color: #e8f5e8;
    color: #2e7d32;
    font-weight: bold;
    padding: 1px 2px;
    border-radius: 2px;
}

/* Badge styles */
.badge {
    font-size: 0.75em;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Button styles */
.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* File input styles */
#fastqFile {
    display: none;
}

/* Results container */
.results-container {
    max-height: none;
    overflow-y: visible;
}

/* Summary statistics */
.summary-stat {
    text-align: center;
    padding: 10px;
}

.summary-stat h4 {
    margin-bottom: 5px;
    font-weight: bold;
}

.summary-stat p {
    margin-bottom: 0;
    font-size: 0.9em;
    opacity: 0.9;
}

/* Error and success states */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-muted {
    color: #6c757d !important;
}

/* Sequence link styling */
.sequence-link {
    text-decoration: none;
    color: #007bff;
    transition: all 0.2s ease;
}

.sequence-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.sequence-link code {
    background-color: #e9ecef;
    color: #495057;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.sequence-link:hover code {
    background-color: #007bff;
    color: white;
}
