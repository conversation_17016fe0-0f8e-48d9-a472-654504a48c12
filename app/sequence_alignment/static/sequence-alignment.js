// Multiple Sequence Alignment JavaScript

let alignmentResults = null;

// Tab switching functions
function showPairwiseTab() {
    const pairwiseContent = document.getElementById('pairwise');
    const multipleContent = document.getElementById('multiple');
    const pairwiseTab = document.getElementById('pairwise-tab');
    const multipleTab = document.getElementById('multiple-tab');

    if (pairwiseContent && multipleContent) {
        // Show pairwise tab content using CSS classes
        pairwiseContent.classList.add('active-tab');
        multipleContent.classList.remove('active-tab');

        // Update tab states
        if (pairwiseTab && multipleTab) {
            pairwiseTab.classList.add('active');
            multipleTab.classList.remove('active');

            // Update aria-selected attributes
            pairwiseTab.setAttribute('aria-selected', 'true');
            multipleTab.setAttribute('aria-selected', 'false');
        }
    }
}

function showMultipleTab() {
    const pairwiseContent = document.getElementById('pairwise');
    const multipleContent = document.getElementById('multiple');
    const pairwiseTab = document.getElementById('pairwise-tab');
    const multipleTab = document.getElementById('multiple-tab');

    if (pairwiseContent && multipleContent) {
        // Hide pairwise tab content and show multiple tab content using CSS classes
        pairwiseContent.classList.remove('active-tab');
        multipleContent.classList.add('active-tab');

        // Update tab states
        if (pairwiseTab && multipleTab) {
            pairwiseTab.classList.remove('active');
            multipleTab.classList.add('active');

            // Update aria-selected attributes
            pairwiseTab.setAttribute('aria-selected', 'false');
            multipleTab.setAttribute('aria-selected', 'true');
        }
    }
}



// Make functions globally available for debugging
window.showPairwiseTab = showPairwiseTab;
window.showMultipleTab = showMultipleTab;

// Test function to verify tab structure
window.testTabStructure = function() {
    const pairwiseTab = document.getElementById('pairwise');
    const multipleTab = document.getElementById('multiple');

    return pairwiseTab && multipleTab;
};

// Function to manually create the multiple tab if missing
window.createMultipleTab = function() {
    const tabContent = document.getElementById('alignmentTabContent');
    if (!tabContent) {
        return;
    }

    // Remove any existing multiple tab
    const existingMultiple = document.getElementById('multiple');
    if (existingMultiple) {
        existingMultiple.remove();
    }

    // Create the multiple tab div
    const multipleTab = document.createElement('div');
    multipleTab.className = 'tab-pane';
    multipleTab.id = 'multiple';
    multipleTab.setAttribute('role', 'tabpanel');
    multipleTab.setAttribute('aria-labelledby', 'multiple-tab');

    // Add the complete multiple sequence alignment content
    multipleTab.innerHTML = `
        <div class="row">
            <!-- Input Section -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-file-upload"></i> Multiple Sequences Input</h5>
                    </div>
                    <div class="card-body">
                        <form id="multipleAlignmentForm">
                            <!-- FASTA Input -->
                            <div class="mb-3">
                                <label for="fastaInput" class="form-label">FASTA Sequences</label>
                                <textarea
                                    class="form-control fasta-input"
                                    id="fastaInput"
                                    rows="10"
                                    placeholder=">Sequence1
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
>Sequence2
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGV
>Sequence3
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGA"></textarea>
                                <div class="form-text">
                                    Enter multiple sequences in FASTA format. Each sequence should start with a header line beginning with '>'.
                                </div>
                            </div>

                            <!-- File Upload -->
                            <div class="mb-3">
                                <label for="fastaFile" class="form-label">Or Upload FASTA File</label>
                                <input type="file" class="form-control" id="fastaFile" accept=".fasta,.fa,.fas,.txt">
                                <div class="form-text">
                                    Upload a FASTA file containing multiple sequences.
                                </div>
                            </div>

                            <!-- MUSCLE Parameters -->
                            <div class="card parameter-card mb-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-cogs"></i> MUSCLE Parameters</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="maxIterations" class="form-label">Max Iterations</label>
                                            <input type="number" class="form-control" id="maxIterations" value="16" min="1" max="100">
                                            <div class="form-text">Maximum number of refinement iterations</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="gapPenalty" class="form-label">Gap Penalty</label>
                                            <input type="number" class="form-control" id="gapPenalty" value="-12" step="0.1">
                                            <div class="form-text">Penalty for opening gaps</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-align-justify"></i> Run MUSCLE Alignment
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Multiple Sequence Alignment Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="multipleResults" style="display: none;">
                            <!-- Conservation Summary -->
                            <div class="mb-3">
                                <h6>Conservation Analysis</h6>
                                <div class="conservation-bar" id="conservationBar"></div>
                                <small class="text-muted">
                                    <span style="color: #00aa00;">■</span> Highly conserved
                                    <span style="color: #ffaa00;">■</span> Moderately conserved
                                    <span style="color: #ff4444;">■</span> Variable
                                </small>
                            </div>

                            <!-- Alignment Display -->
                            <div class="msa-result" id="msaAlignment"></div>

                            <!-- Download Options -->
                            <div class="mt-3">
                                <button class="btn btn-outline-primary btn-sm" onclick="downloadMSA('fasta')">
                                    <i class="fas fa-download"></i> Download FASTA
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="downloadMSA('clustal')">
                                    <i class="fas fa-download"></i> Download Clustal
                                </button>
                            </div>
                        </div>

                        <div id="multipleNoResults">
                            <p class="text-muted text-center">
                                <i class="fas fa-info-circle"></i><br>
                                Enter multiple sequences above and click "Run MUSCLE Alignment" to see results.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Insert before the loading divs
    const loadingDiv = document.getElementById('loadingDiv');
    if (loadingDiv && loadingDiv.parentElement === tabContent) {
        tabContent.insertBefore(multipleTab, loadingDiv);
    } else {
        tabContent.appendChild(multipleTab);
    }

    console.log('Multiple tab created:', multipleTab);
    return multipleTab;
};

// DOM elements
const sequence1Input = document.getElementById('sequence1');
const sequence2Input = document.getElementById('sequence2');
const seq1LengthSpan = document.getElementById('seq1Length');
const seq2LengthSpan = document.getElementById('seq2Length');
const alignmentForm = document.getElementById('alignmentForm');
const alignBtn = document.getElementById('alignBtn');
const loadingDiv = document.getElementById('loadingDiv');
const resultsContainer = document.getElementById('resultsContainer');
const alignmentSection = document.getElementById('alignmentSection');
const alignmentDisplay = document.getElementById('alignmentDisplay');

// Parameter inputs
const matchScoreInput = document.getElementById('matchScore');
const mismatchScoreInput = document.getElementById('mismatchScore');
const openGapScoreInput = document.getElementById('openGapScore');
const extendGapScoreInput = document.getElementById('extendGapScore');

// Update sequence length counters
function updateSequenceLength() {
    const seq1 = sequence1Input.value.replace(/\s/g, '');
    const seq2 = sequence2Input.value.replace(/\s/g, '');
    seq1LengthSpan.textContent = seq1.length;
    seq2LengthSpan.textContent = seq2.length;
}

// Event listeners for sequence inputs
sequence1Input.addEventListener('input', updateSequenceLength);
sequence2Input.addEventListener('input', updateSequenceLength);

// Display results summary
function displayResults(results) {
    const { sequence1, sequence2, alignment_score, num_mutations, mutations, parameters } = results;
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-body text-center">
                        <h5 class="card-title text-success">Alignment Score</h5>
                        <h2 class="text-success">${alignment_score}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        <h5 class="card-title text-warning">Mutations</h5>
                        <h2 class="text-warning">${num_mutations}</h2>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <h6><i class="fas fa-info-circle"></i> Sequence Information</h6>
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>Sequence 1 Length:</strong> ${sequence1.length} bp<br>
                        <strong>Sequence 2 Length:</strong> ${sequence2.length} bp
                    </small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>Match Score:</strong> ${parameters.match_score}<br>
                        <strong>Gap Open:</strong> ${parameters.open_gap_score}
                    </small>
                </div>
            </div>
        </div>
        
        ${mutations.length > 0 ? `
        <div class="mt-3">
            <h6><i class="fas fa-list"></i> Mutations Summary</h6>
            <div class="mutations-list" style="max-height: 200px; overflow-y: auto;">
                ${mutations.map(mut => `
                    <div class="mutation-item small">
                        <span class="badge bg-warning text-dark">Pos ${mut[0]}</span>
                        ${mut[1]} → ${mut[2]}
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}
        
        <div class="mt-3">
            <button class="btn btn-outline-primary btn-sm" onclick="scrollToAlignment()">
                <i class="fas fa-eye"></i> View Detailed Alignment
            </button>
        </div>
    `;
    
    resultsContainer.innerHTML = html;
}

// Display detailed alignment with highlighting
function displayDetailedAlignment(alignmentData) {
    const { aligned_seq1, aligned_seq2, mutation_positions } = alignmentData;

    // Create highlighted sequences by building arrays first, then joining
    let seq1Parts = [];
    let seq2Parts = [];

    for (let i = 0; i < aligned_seq1.length; i++) {
        const char1 = aligned_seq1[i];
        const char2 = aligned_seq2[i];

        if (mutation_positions.includes(i)) {
            // Mutation/mismatch
            seq1Parts.push(`<span class="mutation">${char1}</span>`);
            seq2Parts.push(`<span class="mutation">${char2}</span>`);
        } else {
            seq1Parts.push(char1);
            seq2Parts.push(char2);
        }
    }

    // Split into chunks for better display
    const chunkSize = 60;
    let alignmentHtml = '';

    // Process chunks by character count, not by HTML string length
    for (let i = 0; i < aligned_seq1.length; i += chunkSize) {
        const endPos = Math.min(i + chunkSize, aligned_seq1.length);

        // Extract the chunk from the original sequences
        const originalChunk1 = aligned_seq1.slice(i, endPos);
        const originalChunk2 = aligned_seq2.slice(i, endPos);

        // Apply highlighting to the chunk
        let chunk1Html = '';
        let chunk2Html = '';

        for (let j = 0; j < originalChunk1.length; j++) {
            const globalPos = i + j;
            const char1 = originalChunk1[j];
            const char2 = originalChunk2[j];

            if (mutation_positions.includes(globalPos)) {
                chunk1Html += `<span class="mutation">${char1}</span>`;
                chunk2Html += `<span class="mutation">${char2}</span>`;
            } else {
                chunk1Html += char1;
                chunk2Html += char2;
            }
        }

        const position = i + 1;

        alignmentHtml += `<div class="alignment-chunk">`;
        alignmentHtml += `<div><span class="sequence-label">Seq1 ${position}:</span>${chunk1Html}</div>`;
        alignmentHtml += `<div><span class="sequence-label">Seq2 ${position}:</span>${chunk2Html}</div>`;
        alignmentHtml += `<div style="margin-bottom: 1rem;"></div>`;
        alignmentHtml += `</div>`;
    }

    alignmentDisplay.innerHTML = alignmentHtml;
    alignmentSection.style.display = 'block';
}

// Scroll to alignment section
function scrollToAlignment() {
    alignmentSection.scrollIntoView({ behavior: 'smooth' });
}

// Form submission
alignmentForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const sequence1 = sequence1Input.value.trim();
    const sequence2 = sequence2Input.value.trim();
    
    // Validation
    if (!sequence1 || !sequence2) {
        alert('Please enter both sequences');
        return;
    }
    
    // Show loading
    loadingDiv.style.display = 'block';
    alignBtn.disabled = true;
    alignmentSection.style.display = 'none';
    
    try {
        const formData = new FormData();
        formData.append('sequence1', sequence1);
        formData.append('sequence2', sequence2);
        formData.append('match_score', matchScoreInput.value);
        formData.append('mismatch_score', mismatchScoreInput.value);
        formData.append('open_gap_score', openGapScoreInput.value);
        formData.append('extend_gap_score', extendGapScoreInput.value);

        const response = await window.pathUtils.fetch('/align-two-sequences', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Alignment failed');
        }

        alignmentResults = await response.json();
        displayResults(alignmentResults);
        displayDetailedAlignment(alignmentResults.alignment_data);
        
    } catch (error) {
        alert('Error: ' + error.message);
        console.error('Alignment error:', error);
        resultsContainer.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Error</h6>
                <p>${error.message}</p>
            </div>
        `;
    } finally {
        // Hide loading
        loadingDiv.style.display = 'none';
        alignBtn.disabled = false;
    }
});

// Initialize
updateSequenceLength();

// Multiple Sequence Alignment Variables
let msaResults = null;

// Handle FASTA file upload
function handleFastaFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('fastaInput').value = e.target.result;
        };
        reader.readAsText(file);
    }
}

// Handle multiple sequence alignment form submission
async function handleMultipleAlignmentSubmit(event) {
    event.preventDefault();

    const fastaInput = document.getElementById('fastaInput');
    const fastaSequences = fastaInput.value.trim();

    if (!fastaSequences) {
        alert('Please enter FASTA sequences or upload a file.');
        return;
    }

    // Validate FASTA format
    if (!fastaSequences.includes('>')) {
        alert('Please enter sequences in FASTA format (starting with >).');
        return;
    }

    const maxIterations = document.getElementById('maxIterations').value;
    const gapPenalty = document.getElementById('gapPenalty').value;

    // Show loading
    const multipleLoadingDiv = document.getElementById('multipleLoadingDiv');
    const multipleResults = document.getElementById('multipleResults');
    const multipleNoResults = document.getElementById('multipleNoResults');

    multipleLoadingDiv.style.display = 'block';
    multipleResults.style.display = 'none';
    multipleNoResults.style.display = 'none';

    try {
        const formData = new FormData();
        formData.append('fasta_sequences', fastaSequences);
        formData.append('max_iterations', maxIterations);
        formData.append('gap_penalty', gapPenalty);

        const response = await window.pathUtils.fetch('/align-multiple-sequences', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            msaResults = result;
            displayMultipleAlignmentResults(result);
        } else {
            throw new Error(result.detail || 'Multiple sequence alignment failed');
        }

    } catch (error) {
        console.error('Multiple alignment error:', error);
        alert(`Error: ${error.message}`);
        multipleNoResults.style.display = 'block';
    } finally {
        multipleLoadingDiv.style.display = 'none';
    }
}

// Display multiple sequence alignment results
function displayMultipleAlignmentResults(result) {
    const multipleResults = document.getElementById('multipleResults');
    const multipleNoResults = document.getElementById('multipleNoResults');
    const msaAlignment = document.getElementById('msaAlignment');
    const msaLegend = document.getElementById('msaLegend');

    // Show results container
    multipleResults.style.display = 'block';
    multipleNoResults.style.display = 'none';

    // Display conservation bar
    displayConservationBar(result.conservation_scores);

    // Display alignment with color coding
    const colorizedAlignment = colorizeAlignment(result.formatted_alignment);
    msaAlignment.innerHTML = colorizedAlignment;

    // Show color legend
    if (msaLegend) {
        msaLegend.style.display = 'flex';

        // Update legend based on sequence type
        const isProtein = /[DEFHIKLMNPQRSVWY]/.test(result.formatted_alignment.toUpperCase());
        updateLegend(msaLegend, isProtein);
    }

    // Update summary info (remove any existing summary first)
    const existingSummary = multipleResults.querySelector('.alignment-summary');
    if (existingSummary) {
        existingSummary.remove();
    }

    const summaryInfo = document.createElement('div');
    summaryInfo.className = 'mb-3 alignment-summary';
    summaryInfo.innerHTML = `
        <small class="text-muted">
            <strong>Alignment Summary:</strong>
            ${result.num_sequences} sequences,
            ${result.alignment_length} positions,
            ${result.parameters.max_iterations} iterations
        </small>
    `;

    // Insert summary before alignment
    msaAlignment.parentNode.insertBefore(summaryInfo, msaAlignment);
}

// Colorize alignment for better readability - only color mismatched columns
function colorizeAlignment(alignmentText) {
    if (!alignmentText) return '';

    // Define color schemes for different amino acids and nucleotides
    const aminoAcidColors = {
        // Hydrophobic amino acids - blue shades
        'A': '#4A90E2', 'V': '#4A90E2', 'I': '#4A90E2', 'L': '#4A90E2', 'M': '#4A90E2', 'F': '#4A90E2', 'W': '#4A90E2', 'P': '#4A90E2',
        // Polar amino acids - green shades
        'S': '#7ED321', 'T': '#7ED321', 'N': '#7ED321', 'Q': '#7ED321', 'Y': '#7ED321', 'C': '#7ED321',
        // Positively charged - red shades
        'K': '#D0021B', 'R': '#D0021B', 'H': '#D0021B',
        // Negatively charged - orange shades
        'D': '#F5A623', 'E': '#F5A623',
        // Special cases
        'G': '#9013FE', // Glycine - purple
        '-': '#CCCCCC', // Gaps - gray
        '*': '#FF6B6B'  // Stop codons - bright red
    };

    const nucleotideColors = {
        'A': '#FF6B6B', // Adenine - red
        'T': '#4ECDC4', // Thymine - teal
        'G': '#45B7D1', // Guanine - blue
        'C': '#96CEB4', // Cytosine - green
        'U': '#4ECDC4', // Uracil - teal (same as T)
        'N': '#DDA0DD', // Unknown - plum
        '-': '#CCCCCC', // Gaps - gray
        '*': '#FF6B6B'  // Stop - red
    };

    // Auto-detect if this is protein or nucleotide sequence
    const isProtein = /[DEFHIKLMNPQRSVWY]/.test(alignmentText.toUpperCase());
    const colorMap = isProtein ? aminoAcidColors : nucleotideColors;

    // Parse the MUSCLE alignment format
    const lines = alignmentText.split('\n');
    const blocks = [];
    let currentBlock = [];

    // Group lines into blocks (separated by empty lines)
    for (const line of lines) {
        if (line.trim() === '') {
            if (currentBlock.length > 0) {
                blocks.push(currentBlock);
                currentBlock = [];
            }
        } else {
            currentBlock.push(line);
        }
    }
    if (currentBlock.length > 0) {
        blocks.push(currentBlock);
    }

    // Extract all sequences by combining blocks
    const allSequences = {};
    const sequenceOrder = [];

    for (const block of blocks) {
        for (const line of block) {
            // Skip position indicator lines
            if (/^\s*Position:\s*\d+/.test(line)) continue;

            const parts = line.split(/\s+/);
            if (parts.length >= 2) {
                const seqName = parts[0];
                const seqPart = parts.slice(1).join('');

                if (!allSequences[seqName]) {
                    allSequences[seqName] = '';
                    sequenceOrder.push(seqName);
                }
                allSequences[seqName] += seqPart;
            }
        }
    }

    // Find mismatched columns across all sequences
    const mismatchedColumns = new Set();
    const sequenceNames = sequenceOrder;
    const sequences = sequenceNames.map(name => allSequences[name]);

    if (sequences.length > 1) {
        const maxLength = Math.max(...sequences.map(s => s.length));

        for (let pos = 0; pos < maxLength; pos++) {
            const residuesAtPosition = sequences.map(s => s[pos] || '-').map(r => r.toUpperCase());
            const uniqueResidues = new Set(residuesAtPosition.filter(r => r !== '-'));

            // Column has mismatch if there are multiple different non-gap residues
            // or if there are both gaps and non-gaps
            const hasGaps = residuesAtPosition.includes('-');
            const hasNonGaps = residuesAtPosition.some(r => r !== '-');

            if (uniqueResidues.size > 1 || (hasGaps && hasNonGaps)) {
                mismatchedColumns.add(pos);
            }
        }
    }

    // Now colorize the original formatted text
    const colorizedLines = lines.map((line) => {
        if (!line.trim()) return line; // Empty lines

        // Skip position indicator lines
        if (/^\s*Position:\s*\d+/.test(line)) return line;

        const parts = line.split(/\s+/);
        if (parts.length < 2) return line; // Header or spacer line

        const sequenceName = parts[0];
        const sequencePart = parts.slice(1).join('');

        // Find the starting position of this block in the full sequence
        let blockStartPos = 0;
        if (allSequences[sequenceName]) {
            const fullSeq = allSequences[sequenceName];
            blockStartPos = fullSeq.indexOf(sequencePart);
        }

        // Colorize each character in the sequence part
        const colorizedSequence = sequencePart.split('').map((char, localPos) => {
            const globalPos = blockStartPos + localPos;
            const upperChar = char.toUpperCase();
            const shouldColor = mismatchedColumns.has(globalPos);

            if (shouldColor) {
                const color = colorMap[upperChar];
                if (color) {
                    return `<span style="background-color: ${color}; color: white; padding: 1px 2px; margin: 0 1px; border-radius: 2px; font-weight: bold;">${char}</span>`;
                } else {
                    return `<span style="background-color: #E0E0E0; color: #333; padding: 1px 2px; margin: 0 1px; border-radius: 2px; font-weight: bold;">${char}</span>`;
                }
            } else {
                // No color for conserved positions
                return char;
            }
        }).join('');

        // Return formatted line with sequence name and colorized sequence
        return `<span style="font-weight: bold; color: #333; display: inline-block; width: 120px; text-align: left;">${sequenceName}</span>  ${colorizedSequence}`;
    });

    return colorizedLines.join('\n');
}

// Update legend based on sequence type
function updateLegend(legendElement, isProtein) {
    const headerNote = '<div style="width: 100%; margin-bottom: 8px;"><small class="text-muted"><strong>Colors shown only for mismatched columns:</strong></small></div>';

    if (isProtein) {
        // Protein legend
        legendElement.innerHTML = headerNote + `
            <div class="legend-item">
                <div class="legend-color hydrophobic"></div>
                <span>Hydrophobic (A,V,I,L,M,F,W,P)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color polar"></div>
                <span>Polar (S,T,N,Q,Y,C)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color positive"></div>
                <span>Positive (K,R,H)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color negative"></div>
                <span>Negative (D,E)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color special"></div>
                <span>Glycine (G)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color gap"></div>
                <span>Gaps (-)</span>
            </div>
        `;
    } else {
        // Nucleotide legend
        legendElement.innerHTML = headerNote + `
            <div class="legend-item">
                <div class="legend-color adenine"></div>
                <span>Adenine (A)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color thymine"></div>
                <span>Thymine/Uracil (T/U)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color guanine"></div>
                <span>Guanine (G)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color cytosine"></div>
                <span>Cytosine (C)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color unknown"></div>
                <span>Unknown (N)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color gap"></div>
                <span>Gaps (-)</span>
            </div>
        `;
    }
}

// Display conservation bar
function displayConservationBar(conservationScores) {
    const conservationBar = document.getElementById('conservationBar');

    if (!conservationScores || conservationScores.length === 0) {
        conservationBar.style.display = 'none';
        return;
    }

    // Create conservation visualization
    const canvas = document.createElement('canvas');
    canvas.width = Math.min(conservationScores.length * 2, 800);
    canvas.height = 20;

    const ctx = canvas.getContext('2d');
    const barWidth = canvas.width / conservationScores.length;

    conservationScores.forEach((score, index) => {
        // Color based on conservation score
        let color;
        if (score >= 0.8) {
            color = '#00aa00'; // High conservation - green
        } else if (score >= 0.5) {
            color = '#ffaa00'; // Medium conservation - orange
        } else {
            color = '#ff4444'; // Low conservation - red
        }

        ctx.fillStyle = color;
        ctx.fillRect(index * barWidth, 0, barWidth, 20);
    });

    // Replace conservation bar content
    conservationBar.innerHTML = '';
    conservationBar.appendChild(canvas);
    conservationBar.style.display = 'block';
}

// Download MSA results
function downloadMSA(format) {
    if (!msaResults) {
        alert('No alignment results to download.');
        return;
    }

    let content = '';
    let filename = '';

    if (format === 'fasta') {
        // Generate FASTA format
        for (let i = 0; i < msaResults.sequence_names.length; i++) {
            content += `>${msaResults.sequence_names[i]}\n${msaResults.sequences[i]}\n`;
        }
        filename = 'multiple_alignment.fasta';
    } else if (format === 'clustal') {
        // Generate Clustal format
        content = 'CLUSTAL W multiple sequence alignment\n\n';
        content += msaResults.formatted_alignment;
        filename = 'multiple_alignment.aln';
    }

    // Create download link
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}


// Initialize multiple sequence alignment event listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing tabs');

    // Test tab structure first
    testTabStructure();

    // Check if multiple tab exists, if not create it
    let multipleTab = document.getElementById('multiple');
    if (!multipleTab) {
        console.log('Multiple tab not found, creating it...');
        multipleTab = createMultipleTab();
    }

    // Add event listeners to tab buttons
    const pairwiseTabButton = document.getElementById('pairwise-tab');
    const multipleTabButton = document.getElementById('multiple-tab');

    if (pairwiseTabButton) {
        pairwiseTabButton.addEventListener('click', function(e) {
            e.preventDefault();
            showPairwiseTab();
        });
    }

    if (multipleTabButton) {
        multipleTabButton.addEventListener('click', function(e) {
            e.preventDefault();
            showMultipleTab();
        });
    }

    // Initialize tab display - show pairwise by default
    setTimeout(() => {
        showPairwiseTab();
    }, 100);

    // Set up multiple sequence alignment form handlers
    setTimeout(() => {
        const fastaFile = document.getElementById('fastaFile');
        const multipleAlignmentForm = document.getElementById('multipleAlignmentForm');

        if (fastaFile) {
            fastaFile.addEventListener('change', handleFastaFileUpload);
        }

        if (multipleAlignmentForm) {
            multipleAlignmentForm.addEventListener('submit', handleMultipleAlignmentSubmit);
        }
    }, 200);
});
