<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sequence Alignment Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/styles.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <!-- Navigation Buttons -->
        <div class="mb-3 d-flex justify-content-between">
            <a href="/" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Main
            </a>
            <a href="#" class="btn btn-outline-primary" onclick="viewPastJobs()">
                <i class="fas fa-history"></i> View Past Jobs
            </a>
        </div>

        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-dna"></i> FASTQ File Alignment
                </h1>
                <p class="text-center text-muted">Upload FASTQ files and align sequences with a reference sequence</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-upload"></i> Upload & Configure</h5>
                    </div>
                    <div class="card-body">
                        <form id="alignmentForm">
                            <!-- File Upload -->
                            <div class="mb-3">
                                <label for="fastqFile" class="form-label">FASTQ File</label>
                                <div class="upload-area" id="uploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <p>Drag & drop your FASTQ file here or click to browse</p>
                                    <input type="file" class="form-control d-none" id="fastqFile" accept=".fastq,.fq" required>
                                    <button type="button" class="btn btn-outline-primary" id="chooseFileBtn">
                                        Choose File
                                    </button>
                                </div>
                                <div id="fileInfo" class="mt-2 text-muted"></div>
                            </div>

                            <!-- Job Name -->
                            <div class="mb-3">
                                <label for="jobName" class="form-label">Job Name (optional)</label>
                                <input type="text" class="form-control" id="jobName"
                                    placeholder="Enter a name for this alignment job">
                            </div>

                            <!-- Reference Sequence -->
                            <div class="mb-3">
                                <label for="referenceSeq" class="form-label">Reference Sequence</label>
                                <textarea class="form-control" id="referenceSeq" rows="4"
                                    placeholder="Enter your reference sequence (DNA/RNA)..." required></textarea>
                            </div>

                            <!-- Alignment Parameters -->
                            <div class="card parameter-card mb-3" style="background-color: #f8f9fa;">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-sliders-h"></i> Alignment Parameters
                                        <button type="button" class="btn btn-sm btn-outline-info float-end" data-bs-toggle="collapse" data-bs-target="#fastqParametersCollapse">
                                            <i class="fas fa-cog"></i> Configure
                                        </button>
                                    </h6>
                                </div>
                                <div class="collapse" id="fastqParametersCollapse">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="fastqMatchScore" class="form-label">Match Score</label>
                                                    <input type="number" class="form-control" id="fastqMatchScore" value="1.0" step="0.1">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="fastqMismatchScore" class="form-label">Mismatch Score</label>
                                                    <input type="number" class="form-control" id="fastqMismatchScore" value="-1.0" step="0.1">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="fastqOpenGapScore" class="form-label">Open Gap Score</label>
                                                    <input type="number" class="form-control" id="fastqOpenGapScore" value="-2.0" step="0.1">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="fastqExtendGapScore" class="form-label">Extend Gap Score</label>
                                                    <input type="number" class="form-control" id="fastqExtendGapScore" value="-0.5" step="0.1">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-info-circle"></i>
                                            Higher match scores and lower gap penalties favor longer alignments.
                                            Adjust these values based on your sequence type and analysis needs.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Info Note -->
                            <div class="alert alert-info small mb-3">
                                <i class="fas fa-info-circle"></i>
                                <strong>Note:</strong> Results will be displayed immediately below and automatically saved to your job history for future reference.
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="btn btn-primary w-100" id="alignBtn">
                                <i class="fas fa-align-left"></i> Align Sequences
                            </button>
                            
                            <!-- Loading Indicator -->
                            <div class="loading text-center mt-3" id="loadingDiv">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Processing alignment...</p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> Results Summary</h5>
                    </div>
                    <div class="card-body" id="summaryContainer">
                        <p class="text-muted text-center">Upload a FASTQ file and reference sequence to see alignment results</p>
                    </div>
                </div>

                <!-- Distribution Charts -->
                <div class="row mt-3" id="distributionCharts" style="display: none;">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-line"></i> Distribution Analysis</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-12 mb-4">
                                        <h6 class="text-center mb-3">Sequence Length Distribution</h6>
                                        <div style="height: 250px;">
                                            <canvas id="lengthChart"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <h6 class="text-center mb-3">Alignment Score Distribution</h6>
                                        <div style="height: 250px;">
                                            <canvas id="scoreChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Results -->
        <div class="row mt-4" id="detailedResults" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> Detailed Results</h5>
                        <button class="btn btn-sm btn-outline-primary" id="downloadBtn">
                            <i class="fas fa-download"></i> Download Results
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- Tabs Navigation -->
                        <ul class="nav nav-tabs nav-justified" id="resultsTabs" role="tablist" style="display: none;">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="sequences-tab" data-bs-toggle="tab" data-bs-target="#sequences-pane" type="button" role="tab">
                                    <i class="fas fa-dna"></i> Sequences
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="mutations-tab" data-bs-toggle="tab" data-bs-target="#mutations-pane" type="button" role="tab">
                                    <i class="fas fa-exclamation-triangle"></i> Mutation Analysis
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="resultsTabContent">
                            <!-- Sequences Tab -->
                            <div class="tab-pane fade show active" id="sequences-pane" role="tabpanel">
                                <!-- Results Count -->
                                <div class="mb-2" id="resultsCount" style="display: none;">
                                    <small class="text-muted">Alignment results will appear here after processing</small>
                                </div>

                                <div class="results-container" id="resultsContainer">
                                    <!-- Results will be populated here -->
                                </div>
                            </div>

                            <!-- Mutations Tab -->
                            <div class="tab-pane fade" id="mutations-pane" role="tabpanel">
                                <div class="mt-3">
                                    <h6><i class="fas fa-chart-bar"></i> Mutation Analysis</h6>
                                    <div id="mutationAnalysis">
                                        <!-- Mutation analysis will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Past Jobs Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-history"></i> Past FASTQ Alignment Jobs</h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshJobsList()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="jobsListContainer">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading jobs...</span>
                                </div>
                                <p class="mt-2">Loading jobs...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="static/path-utils.js"></script>
    <script src="static/fastq-alignment.js"></script>
</body>
</html>
