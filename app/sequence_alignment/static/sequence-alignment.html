<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sequence Alignment Tool</title>

    <!-- External CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/styles.css" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        /* ==========================================================================
           LAYOUT & NAVIGATION
           ========================================================================== */
        .back-button {
            margin-bottom: 1rem;
        }

        .nav-tabs .nav-link.active {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd;
        }

        .tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            padding: 1.5rem;
            background-color: white;
        }

        /* Tab Pane Visibility Control */
        .tab-pane {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
        }

        .tab-pane.active-tab {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
            overflow: visible !important;
        }

        /* ==========================================================================
           FORM COMPONENTS
           ========================================================================== */
        .sequence-input, .fasta-input {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .fasta-input {
            font-size: 12px;
            min-height: 200px;
        }

        .parameter-card {
            background-color: #f8f9fa;
        }

        /* ==========================================================================
           ALIGNMENT DISPLAY
           ========================================================================== */
        .alignment-display {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            overflow-x: auto;
            white-space: pre;
        }

        .msa-result {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            overflow-x: auto;
            white-space: pre-line;
            border: 1px solid #dee2e6;
            max-height: 500px;
            overflow-y: auto;
        }

        /* MSA Color Legend */
        .msa-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 11px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 2px;
            border: 1px solid #ccc;
        }

        /* Amino Acid Colors */
        .hydrophobic { background-color: #4A90E2; }
        .polar { background-color: #7ED321; }
        .positive { background-color: #D0021B; }
        .negative { background-color: #F5A623; }
        .special { background-color: #9013FE; }
        .gap { background-color: #CCCCCC; }

        /* Nucleotide Colors */
        .adenine { background-color: #FF6B6B; }
        .thymine { background-color: #4ECDC4; }
        .guanine { background-color: #45B7D1; }
        .cytosine { background-color: #96CEB4; }
        .unknown { background-color: #DDA0DD; }

        .sequence-label {
            font-weight: bold;
            margin-right: 10px;
            display: inline-block;
            width: 80px;
        }

        .mutation {
            background-color: #ffeb3b;
            color: #000;
            padding: 0 2px;
        }

        .conservation-bar {
            height: 20px;
            background: linear-gradient(to right, #ff4444, #ffaa00, #00aa00);
            margin-bottom: 10px;
            border-radius: 3px;
        }

        /* MSA Display Enhancements */
        .msa-result span {
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }

        .msa-result .sequence-name {
            font-weight: bold;
            color: #495057;
            margin-right: 10px;
        }

        /* Improved scrollbars for MSA */
        .msa-result::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .msa-result::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .msa-result::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .msa-result::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- ==========================================================================
             HEADER SECTION
             ========================================================================== -->
        <header>
            <!-- Navigation -->
            <div class="back-button">
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Main
                </a>
            </div>

            <!-- Page Title -->
            <div class="row">
                <div class="col-12">
                    <h1 class="text-center mb-4">
                        <i class="fas fa-align-left"></i> Sequence Alignment Tool
                    </h1>
                    <p class="text-center text-muted">
                        Align sequences using pairwise or multiple sequence alignment algorithms
                    </p>
                </div>
            </div>
        </header>

        <!-- ==========================================================================
             TAB NAVIGATION
             ========================================================================== -->
        <nav class="row">
            <div class="col-12">
                <ul class="nav nav-tabs" id="alignmentTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="pairwise-tab" type="button" role="tab"
                                aria-controls="pairwise" aria-selected="true">
                            <i class="fas fa-align-left"></i> Pairwise Alignment
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="multiple-tab" type="button" role="tab"
                                aria-controls="multiple" aria-selected="false">
                            <i class="fas fa-align-justify"></i> Multiple Sequence Alignment (MUSCLE)
                        </button>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- ==========================================================================
             MAIN CONTENT AREA
             ========================================================================== -->
        <main class="tab-content" id="alignmentTabContent">

            <!-- ==========================================================================
                 PAIRWISE ALIGNMENT TAB
                 ========================================================================== -->
            <section class="tab-pane active-tab" id="pairwise" role="tabpanel" aria-labelledby="pairwise-tab">
                <div class="row">
                    <!-- Input Panel -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-edit"></i> Input Sequences</h5>
                            </div>
                            <div class="card-body">
                                <form id="alignmentForm">
                                    <!-- Sequence 1 Input -->
                                    <div class="mb-3">
                                        <label for="sequence1" class="form-label">
                                            Sequence 1 (Reference)
                                        </label>
                                        <textarea
                                            class="form-control sequence-input"
                                            id="sequence1"
                                            rows="4"
                                            placeholder="Enter first sequence (DNA, RNA, or protein)..."
                                            required></textarea>
                                        <div class="form-text">
                                            <span id="seq1Length">0</span> characters
                                        </div>
                                    </div>

                                    <!-- Sequence 2 Input -->
                                    <div class="mb-3">
                                        <label for="sequence2" class="form-label">
                                            Sequence 2 (Query)
                                        </label>
                                        <textarea
                                            class="form-control sequence-input"
                                            id="sequence2"
                                            rows="4"
                                            placeholder="Enter second sequence (DNA, RNA, or protein)..."
                                            required></textarea>
                                        <div class="form-text">
                                            <span id="seq2Length">0</span> characters
                                        </div>
                                    </div>

                                    <!-- Alignment Parameters -->
                                    <div class="card parameter-card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-sliders-h"></i> Alignment Parameters
                                                <button type="button" class="btn btn-sm btn-outline-info float-end"
                                                        data-bs-toggle="collapse" data-bs-target="#parametersCollapse">
                                                    <i class="fas fa-cog"></i> Configure
                                                </button>
                                            </h6>
                                        </div>
                                        <div class="collapse" id="parametersCollapse">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="matchScore" class="form-label">Match Score</label>
                                                            <input type="number" class="form-control" id="matchScore"
                                                                   value="1.0" step="0.1">
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="mismatchScore" class="form-label">Mismatch Score</label>
                                                            <input type="number" class="form-control" id="mismatchScore"
                                                                   value="-1.0" step="0.1">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="openGapScore" class="form-label">Open Gap Score</label>
                                                            <input type="number" class="form-control" id="openGapScore"
                                                                   value="-2.0" step="0.1">
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="extendGapScore" class="form-label">Extend Gap Score</label>
                                                            <input type="number" class="form-control" id="extendGapScore"
                                                                   value="-0.5" step="0.1">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text-muted small">
                                                    <i class="fas fa-info-circle"></i>
                                                    Higher match scores and lower gap penalties favor longer alignments.
                                                    Adjust these values based on your sequence type and analysis needs.
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success btn-lg" id="alignBtn">
                                            <i class="fas fa-play"></i> Align Sequences
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Results Panel -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-bar"></i> Alignment Results</h5>
                            </div>
                            <div class="card-body" id="resultsContainer">
                                <p class="text-muted text-center">
                                    Enter two sequences and click "Align Sequences" to see results
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Alignment Display -->
                <div class="row mt-4" id="alignmentSection" style="display: none;">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-align-left"></i> Detailed Alignment</h5>
                            </div>
                            <div class="card-body">
                                <div id="alignmentDisplay" class="alignment-display">
                                    <!-- Alignment will be displayed here -->
                                </div>
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <small class="text-muted">
                                                <span class="mutation">■</span> Mutations/Mismatches
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- End Pairwise Alignment Tab -->

            <!-- ==========================================================================
                 MULTIPLE SEQUENCE ALIGNMENT TAB
                 ========================================================================== -->
            <section class="tab-pane" id="multiple" role="tabpanel" aria-labelledby="multiple-tab">
                <!-- Input Section -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-file-upload"></i> Multiple Sequences Input</h5>
                            </div>
                            <div class="card-body">
                                <form id="multipleAlignmentForm">
                                    <!-- FASTA Text Input -->
                                    <div class="mb-3">
                                        <label for="fastaInput" class="form-label">FASTA Sequences</label>
                                        <textarea
                                            class="form-control fasta-input"
                                            id="fastaInput"
                                            rows="10"
                                            placeholder=">Sequence1
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
>Sequence2
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGV
>Sequence3
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGA"></textarea>
                                        <div class="form-text">
                                            Enter multiple sequences in FASTA format. Each sequence should start with a header line beginning with '>'.
                                        </div>
                                    </div>

                                    <div class="row">
                                        <!-- File Upload Option -->
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="fastaFile" class="form-label">Or Upload FASTA File</label>
                                                <input type="file" class="form-control" id="fastaFile"
                                                       accept=".fasta,.fa,.fas,.txt">
                                                <div class="form-text">
                                                    Upload a FASTA file containing multiple sequences.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- MUSCLE Parameters -->
                                        <div class="col-md-6">
                                            <div class="card parameter-card mb-3">
                                                <div class="card-header">
                                                    <h6><i class="fas fa-cogs"></i> MUSCLE Parameters</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <label for="maxIterations" class="form-label">Max Iterations</label>
                                                            <input type="number" class="form-control" id="maxIterations"
                                                                   value="16" min="1" max="100">
                                                            <div class="form-text">Maximum iterations</div>
                                                        </div>
                                                        <div class="col-6">
                                                            <label for="gapPenalty" class="form-label">Gap Penalty</label>
                                                            <input type="number" class="form-control" id="gapPenalty"
                                                                   value="-12" step="0.1">
                                                            <div class="form-text">Gap penalty</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-align-justify"></i> Run MUSCLE Alignment
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> Multiple Sequence Alignment Results</h5>
                            </div>
                            <div class="card-body">
                                <!-- Results Display (Hidden by default) -->
                                <div id="multipleResults" style="display: none;">
                                    <!-- Conservation Analysis -->
                                    <div class="mb-3">
                                        <h6>Conservation Analysis</h6>
                                        <div class="conservation-bar" id="conservationBar"></div>
                                        <small class="text-muted">
                                            <span style="color: #00aa00;">■</span> Highly conserved
                                            <span style="color: #ffaa00;">■</span> Moderately conserved
                                            <span style="color: #ff4444;">■</span> Variable
                                        </small>
                                    </div>

                                    <!-- Color Legend -->
                                    <div class="msa-legend" id="msaLegend" style="display: none;">
                                        <div style="width: 100%; margin-bottom: 8px;">
                                            <small class="text-muted"><strong>Colors shown only for mismatched columns:</strong></small>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color hydrophobic"></div>
                                            <span>Hydrophobic</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color polar"></div>
                                            <span>Polar</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color positive"></div>
                                            <span>Positive</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color negative"></div>
                                            <span>Negative</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color special"></div>
                                            <span>Glycine</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color gap"></div>
                                            <span>Gaps</span>
                                        </div>
                                    </div>

                                    <!-- Alignment Display -->
                                    <div class="msa-result" id="msaAlignment"></div>

                                    <!-- Download Options -->
                                    <div class="mt-3">
                                        <button class="btn btn-outline-primary btn-sm" onclick="downloadMSA('fasta')">
                                            <i class="fas fa-download"></i> Download FASTA
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="downloadMSA('clustal')">
                                            <i class="fas fa-download"></i> Download Clustal
                                        </button>
                                    </div>
                                </div>

                                <!-- No Results Placeholder -->
                                <div id="multipleNoResults">
                                    <p class="text-muted text-center">
                                        <i class="fas fa-info-circle"></i><br>
                                        Enter multiple sequences above and click "Run MUSCLE Alignment" to see results.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- End Multiple Sequence Alignment Tab -->
        </main>
        <!-- End Main Content -->

        <!-- ==========================================================================
             LOADING INDICATORS
             ========================================================================== -->
        <!-- Pairwise Alignment Loading -->
        <div class="loading text-center" id="loadingDiv" style="display: none;">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Aligning sequences...</p>
        </div>

        <!-- Multiple Sequence Alignment Loading -->
        <div class="loading text-center" id="multipleLoadingDiv" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Running MUSCLE alignment...</p>
        </div>
    </div>
    <!-- End Container -->

    <!-- ==========================================================================
         JAVASCRIPT DEPENDENCIES
         ========================================================================== -->
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Application Scripts -->
    <script src="static/path-utils.js"></script>
    <script src="static/sequence-alignment.js"></script>
</body>
</html>
