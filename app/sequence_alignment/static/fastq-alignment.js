// Global variables
let alignmentResults = null;
let selectedFile = null;

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const fastqFile = document.getElementById('fastqFile');
const fileInfo = document.getElementById('fileInfo');
const alignmentForm = document.getElementById('alignmentForm');
const loadingDiv = document.getElementById('loadingDiv');
const alignBtn = document.getElementById('alignBtn');
const summaryContainer = document.getElementById('summaryContainer');
const detailedResults = document.getElementById('detailedResults');
const resultsContainer = document.getElementById('resultsContainer');
const downloadBtn = document.getElementById('downloadBtn');
const chooseFileBtn = document.getElementById('chooseFileBtn');

// Results display elements
const resultsCount = document.getElementById('resultsCount');

// Tab elements
const resultsTabs = document.getElementById('resultsTabs');
const mutationAnalysis = document.getElementById('mutationAnalysis');

// Chart elements
const distributionCharts = document.getElementById('distributionCharts');
const lengthChart = document.getElementById('lengthChart');
const scoreChart = document.getElementById('scoreChart');

// Chart instances
let lengthChartInstance = null;
let scoreChartInstance = null;

// File upload handling
uploadArea.addEventListener('click', (e) => {
    // Don't trigger if clicking on the button inside
    if (e.target !== chooseFileBtn && !chooseFileBtn.contains(e.target)) {
        fastqFile.click();
    }
});
uploadArea.addEventListener('dragover', handleDragOver);
uploadArea.addEventListener('dragleave', handleDragLeave);
uploadArea.addEventListener('drop', handleDrop);

fastqFile.addEventListener('change', handleFileSelect);
chooseFileBtn.addEventListener('click', () => fastqFile.click());

// Event listeners for file handling

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        selectedFile = files[0];
        displayFileInfo(selectedFile);
    }
}

function handleFileSelect() {
    const file = fastqFile.files[0];
    if (file) {
        selectedFile = file;
        displayFileInfo(file);
    }
}

function displayFileInfo(file) {
    fileInfo.innerHTML = `
        <i class="fas fa-file-alt text-success"></i>
        <strong>${file.name}</strong> (${formatFileSize(file.size)})
    `;
}

function resetFileSelection() {
    selectedFile = null;
    fastqFile.value = '';
    fileInfo.innerHTML = '';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Show loading indicator
function showLoadingIndicator() {
    loadingDiv.style.display = 'block';
}

function hideLoadingIndicator() {
    loadingDiv.style.display = 'none';
}

// Form submission
alignmentForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    // Use selectedFile if available (from drag-drop), otherwise use file input
    const file = selectedFile || fastqFile.files[0];
    const referenceSeq = document.getElementById('referenceSeq').value.trim();

    if (!file) {
        alert('Please select a FASTQ file');
        return;
    }
    
    if (!referenceSeq) {
        alert('Please enter a reference sequence');
        return;
    }
    
    // Show loading
    showLoadingIndicator();
    alignBtn.disabled = true;

    try {
        const formData = new FormData();
        formData.append('fastq_file', file);
        formData.append('reference_sequence', referenceSeq);

        // Add job name
        const jobName = document.getElementById('jobName')?.value || '';
        formData.append('job_name', jobName);

        // Add alignment parameters
        const matchScore = document.getElementById('fastqMatchScore')?.value || '1.0';
        const mismatchScore = document.getElementById('fastqMismatchScore')?.value || '-1.0';
        const openGapScore = document.getElementById('fastqOpenGapScore')?.value || '-2.0';
        const extendGapScore = document.getElementById('fastqExtendGapScore')?.value || '-0.5';

        formData.append('match_score', matchScore);
        formData.append('mismatch_score', mismatchScore);
        formData.append('open_gap_score', openGapScore);
        formData.append('extend_gap_score', extendGapScore);

        const response = await window.pathUtils.fetch('/align', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Alignment failed');
        }

        const jobResponse = await response.json();

        // Redirect to job details page instead of displaying results inline
        if (jobResponse.job_id) {
            console.log('Job response:', jobResponse);
            console.log('Redirecting to:', `/fastq-job-details?job_id=${jobResponse.job_id}`);

            // Show success notification with manual link
            const redirectUrl = `fastq-job-details?job_id=${jobResponse.job_id}`;
            showSuccessNotification(`
                Job completed!
                <a href="${redirectUrl}" class="btn btn-sm btn-light ms-2">
                    <i class="fas fa-external-link-alt"></i> View Results
                </a>
            `);

            // Redirect to job details page after a brief delay
            setTimeout(() => {
                console.log('Attempting redirect to:', redirectUrl);
                console.log('Current location:', window.location.href);
                window.location.href = redirectUrl;
            }, 2000);
        } else {
            console.error('No job_id in response:', jobResponse);
        }

    } catch (error) {
        alert('Error: ' + error.message);
        console.error('Alignment error:', error);
    } finally {
        // Hide loading
        hideLoadingIndicator();
        alignBtn.disabled = false;
    }
});



function displaySummary(summary, parameters) {
    summaryContainer.innerHTML = `
        <div class="summary-card p-3 rounded">
            <div class="row text-center">
                <div class="col-md-4">
                    <h4>${summary.total_sequences}</h4>
                    <small>Total Sequences</small>
                </div>
                <div class="col-md-4">
                    <h4>${summary.avg_alignment_score}</h4>
                    <small>Avg Alignment Score</small>
                </div>
                <div class="col-md-4">
                    <h4>${summary.max_alignment_score}</h4>
                    <small>Max Alignment Score</small>
                </div>
            </div>
            <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
            <div class="row text-center">
                <div class="col-md-6">
                    <strong>Score Range:</strong> ${summary.min_alignment_score} - ${summary.max_alignment_score}
                </div>
                <div class="col-md-6">
                    <strong>Avg Mutations:</strong> ${summary.avg_mutations_per_seq}
                </div>
            </div>
            ${parameters ? `
            <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
            <div class="row text-center">
                <div class="col-md-3">
                    <small><strong>Match:</strong> ${parameters.match_score}</small>
                </div>
                <div class="col-md-3">
                    <small><strong>Mismatch:</strong> ${parameters.mismatch_score}</small>
                </div>
                <div class="col-md-3">
                    <small><strong>Gap Open:</strong> ${parameters.open_gap_score}</small>
                </div>
                <div class="col-md-3">
                    <small><strong>Gap Extend:</strong> ${parameters.extend_gap_score}</small>
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

// function displayDetailedResults(sequences) {
//     // Check if sequences is valid
//     console.log("DEBUG!!!!!! In display detailed Results");
//     if (!sequences || !Array.isArray(sequences)) {
//         console.error('Invalid sequences data:', sequences);
//         return;
//     }

//     // Debug: Check first sequence for insertion/deletion values
//     if (sequences.length > 0) {
//         console.log('Displaying sequence:', sequences[0].id, 'insertion:', sequences[0].insertion, 'deletion:', sequences[0].deletion);
//     }

//     const html = sequences.map((seq, index) => `
//         <div class="card sequence-card">
//             <div class="card-header d-flex justify-content-between align-items-center">
//                 <h6 class="mb-0">
//                     <i class="fas fa-dna"></i>
//                     <a href="javascript:void(0)" onclick="openSequenceDetails('${seq.id.replace(/'/g, "\\'")}')" class="sequence-link text-decoration-none">
//                         ${seq.id}
//                     </a>
//                     ${seq.is_reverse_complement ? '<span class="badge bg-warning">Reverse Complement</span>' : ''}
//                     ${seq.insertion ? '<span class="badge bg-info">Insertion</span>' : ''}
//                     ${seq.deletion ? '<span class="badge bg-danger">Deletion</span>' : ''}
//                 </h6>
//                 <span class="badge bg-primary">Score: ${seq.alignment_score}</span>
//             </div>
//             <div class="card-body">
//                 <div class="row">
//                     <div class="col-md-6">
//                         <small class="text-muted">
//                             <strong>Length:</strong> ${seq.length} bp<br>
//                             <strong>Avg Quality:</strong> ${seq.avg_quality}<br>
//                             <strong>Min Quality:</strong> ${seq.min_quality}
//                         </small>
//                     </div>
//                     <div class="col-md-6">
//                         <small class="text-muted">
//                             <strong>Mutations:</strong> ${seq.num_mutations}<br>
//                             <strong>Insertion:</strong> ${seq.insertion ? 'Yes' : 'No'}<br>
//                             <strong>Deletion:</strong> ${seq.deletion ? 'Yes' : 'No'}
//                         </small>
//                     </div>
//                 </div>
//                 ${(seq.mutations && seq.mutations.length > 0) ? `
//                     <div class="mt-2">
//                         <strong>Mutations:</strong><br>
//                         ${seq.mutations.map(mut =>
//                             `<span class="badge mutation-badge bg-secondary">${mut.mutation}</span>`
//                         ).join('')}
//                         ${seq.num_mutations > 20 ? `<span class="text-muted">... and ${seq.num_mutations - 20} more</span>` : ''}
//                     </div>
//                 ` : '<div class="mt-2 text-success"><i class="fas fa-check"></i> No mutations found</div>'}
                
//                 ${seq.alignment ? `
//                     <div class="mt-3">
//                         <strong>Alignment:</strong>
//                         <div class="alignment-container mt-2">
//                             <div class="alignment-sequence">
//                                 <small><strong>Reference:</strong></small><br>
//                                 <code class="alignment-text">${formatAlignmentWithHighlights(seq.alignment.aligned_seq1, seq.alignment.mutation_positions, 0, 'reference')}</code>
//                             </div>
//                             <div class="alignment-sequence mt-1">
//                                 <small><strong>Query:</strong></small><br>
//                                 <code class="alignment-text">${formatAlignmentWithHighlights(seq.alignment.aligned_seq2, seq.alignment.mutation_positions, 0, 'query')}</code>
//                             </div>
//                         </div>
//                     </div>
//                 ` : ''}
//             </div>
//         </div>
//     `).join('');

//     resultsContainer.innerHTML = html;
// }

function displayDistributionCharts(sequences) {
    // Show the charts container
    distributionCharts.style.display = 'block';

    // Prepare data for charts
    const lengths = sequences.map(seq => seq.length);
    const scores = sequences.map(seq => seq.alignment_score);

    // Create length distribution chart
    createLengthDistributionChart(lengths);

    // Create score distribution chart
    createScoreDistributionChart(scores);
}

function createLengthDistributionChart(lengths) {
    // Destroy existing chart if it exists
    if (lengthChartInstance) {
        lengthChartInstance.destroy();
    }

    // Create bins for histogram
    const bins = createHistogramBins(lengths, 10);

    const ctx = lengthChart.getContext('2d');
    lengthChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: bins.labels,
            datasets: [{
                label: 'Number of Sequences',
                data: bins.counts,
                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    top: 10,
                    bottom: 10
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    suggestedMax: Math.max(...bins.counts) * 1.1, // Squeeze y-axis to 110% of max value
                    ticks: {
                        precision: 0,
                        font: {
                            size: 11
                        }
                    },
                    title: {
                        display: true,
                        text: 'Number of Sequences',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: 10
                        },
                        maxRotation: 45
                    },
                    title: {
                        display: true,
                        text: 'Sequence Length (bp)',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleFont: {
                        size: 12
                    },
                    bodyFont: {
                        size: 11
                    }
                }
            }
        }
    });
}

function createScoreDistributionChart(scores) {
    // Destroy existing chart if it exists
    if (scoreChartInstance) {
        scoreChartInstance.destroy();
    }

    // Create bins for histogram
    const bins = createHistogramBins(scores, 10);

    const ctx = scoreChart.getContext('2d');
    scoreChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: bins.labels,
            datasets: [{
                label: 'Number of Sequences',
                data: bins.counts,
                backgroundColor: 'rgba(255, 99, 132, 0.6)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    top: 10,
                    bottom: 10
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    suggestedMax: Math.max(...bins.counts) * 1.1, // Squeeze y-axis to 110% of max value
                    ticks: {
                        precision: 0,
                        font: {
                            size: 11
                        }
                    },
                    title: {
                        display: true,
                        text: 'Number of Sequences',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: 10
                        },
                        maxRotation: 45
                    },
                    title: {
                        display: true,
                        text: 'Alignment Score',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleFont: {
                        size: 12
                    },
                    bodyFont: {
                        size: 11
                    }
                }
            }
        }
    });
}

function createHistogramBins(data, numBins) {
    const min = Math.min(...data);
    const max = Math.max(...data);
    const range = max - min;

    // Adjust number of bins if range is small
    if (range < numBins) {
        numBins = Math.max(range, 3);
    }

    const binSize = range / numBins;
    const bins = Array(numBins).fill(0);
    const labels = [];

    // Create bin labels with better formatting
    for (let i = 0; i < numBins; i++) {
        const binStart = min + (i * binSize);
        const binEnd = min + ((i + 1) * binSize);

        // Format numbers based on their magnitude
        let startLabel, endLabel;
        if (binStart >= 1000) {
            startLabel = (binStart / 1000).toFixed(1) + 'k';
            endLabel = (binEnd / 1000).toFixed(1) + 'k';
        } else {
            startLabel = Math.round(binStart).toString();
            endLabel = Math.round(binEnd).toString();
        }

        labels.push(`${startLabel}-${endLabel}`);
    }

    // Count data points in each bin
    data.forEach(value => {
        let binIndex = Math.floor((value - min) / binSize);
        if (binIndex >= numBins) binIndex = numBins - 1; // Handle edge case
        if (binIndex < 0) binIndex = 0; // Handle negative edge case
        bins[binIndex]++;
    });

    return {
        labels: labels,
        counts: bins
    };
}

function displayMutationAnalysis(mutationAnalysisData) {
    // Use Python-generated mutation analysis data
    if (!mutationAnalysisData) {
        mutationAnalysis.innerHTML = '<div class="alert alert-info">No mutation analysis data available</div>';
        return;
    }

    const stats = mutationAnalysisData.statistics;
    const topMutations = mutationAnalysisData.top_mutations || [];
    const topPatterns = mutationAnalysisData.top_patterns || [];

    // Generate HTML using Python-generated statistics
    let html = `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title text-primary">${stats.total_mutations}</h5>
                        <p class="card-text">Total Mutations</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title text-warning">${stats.mutation_rate}%</h5>
                        <p class="card-text">Sequences with Mutations</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title text-info">${stats.avg_mutations_per_sequence}</h5>
                        <p class="card-text">Avg Mutations per Sequence</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    if (topMutations.length > 0) {
        html += `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-trophy"></i> Top ${Math.min(10, topMutations.length)} Most Frequent Mutations</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Mutation</th>
                                    <th>Frequency</th>
                                    <th>Percentage</th>
                                    <th>Visual</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        topMutations.forEach(mutation => {
            const barWidth = topMutations.length > 0 ? (mutation.count / topMutations[0].count) * 100 : 0;

            html += `
                <tr>
                    <td><span class="badge bg-secondary">${mutation.rank}</span></td>
                    <td><code class="mutation-code">${mutation.mutation}</code></td>
                    <td><strong>${mutation.count}</strong></td>
                    <td>${mutation.percentage}%</td>
                    <td>
                        <div class="progress" style="height: 20px; width: 150px;">
                            <div class="progress-bar bg-danger" role="progressbar"
                                 style="width: ${barWidth}%"
                                 aria-valuenow="${mutation.count}"
                                 aria-valuemin="0"
                                 aria-valuemax="${topMutations[0].count}">
                            </div>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        // Add top 10 most frequent mutation patterns
        if (topPatterns.length > 0) {
            html += `
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-dna"></i> Top ${Math.min(10, topPatterns.length)} Most Frequent Mutation Patterns</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Mutation Pattern</th>
                                        <th>Sequence Count</th>
                                        <th>Percentage</th>
                                        <th>Example Sequences</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            topPatterns.forEach(pattern => {
                const mutationBadges = pattern.mutations.map(mut =>
                    `<span class="badge bg-danger mutation-badge">${mut}</span>`
                ).join(' ');

                // Show first 3 example sequences with clickable links
                const exampleSequences = pattern.example_sequences.map(seq =>
                    `<a href="sequence-details?id=${encodeURIComponent(seq.id)}" class="sequence-link" title="View details for ${seq.id}">
                        <code class="sequence-id">${seq.id}</code>
                    </a>`
                ).join(', ');
                const moreSeqCount = pattern.count > 3 ? ` +${pattern.count - 3} more` : '';

                html += `
                    <tr>
                        <td><span class="badge bg-secondary">${pattern.rank}</span></td>
                        <td>${mutationBadges}</td>
                        <td><strong class="text-primary">${pattern.count}</strong></td>
                        <td>${pattern.percentage}%</td>
                        <td>
                            ${exampleSequences}
                            ${moreSeqCount ? `<small class="text-muted">${moreSeqCount}</small>` : ''}
                        </td>
                    </tr>
                `;
            });

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }
    } else {
        html += `
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle"></i>
                <strong>Excellent!</strong> No mutations were detected in any of the sequences.
            </div>
        `;
    }

    mutationAnalysis.innerHTML = html;
}

function formatAlignmentWithHighlights(sequence, mutationPositions, startPos, type) {
    let result = '';
    // console.log('sequence length', sequence.length);
    for (let i = 0; i < sequence.length; i++) {
        const globalPos = startPos + i;
        const isMutation = mutationPositions.includes(globalPos);
        const char = sequence[i];

        if (isMutation) {
            result += `<span class="mutation-highlight ${type === 'reference' ? 'ref-mutation' : 'query-mutation'}">${char}</span>`;
        } else {
            result += char;
        }
    }
    return result;
}

// Store sequence data in session storage
function storeSequenceData(sequences) {
    try {
        // Create a map of sequence ID to sequence data for easy lookup
        const sequenceMap = {};
        sequences.forEach(seq => {
            sequenceMap[seq.id] = seq;
        });

        // Store in session storage (will persist until browser tab is closed)
        sessionStorage.setItem('sequenceData', JSON.stringify(sequenceMap));
        console.log('Stored sequence data for', sequences.length, 'sequences');
    } catch (error) {
        console.error('Failed to store sequence data:', error);
    }
}

// Open sequence details page
function openSequenceDetails(sequenceId) {
    // Properly encode the sequence ID for URL
    const encodedId = encodeURIComponent(sequenceId);
    const url = window.pathUtils.buildUrl(`sequence-details?id=${encodedId}`);
    window.open(url, '_blank');
}

// Download results
downloadBtn.addEventListener('click', () => {
    if (!alignmentResults) {
        alert('No results to download');
        return;
    }
    
    const dataStr = JSON.stringify(alignmentResults, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'alignment_results.json';
    link.click();
});

// Function to show success notification
function showSuccessNotification(message) {
    // Create a temporary notification element
    const notification = document.createElement('div');
    notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    notification.innerHTML = `
        <i class="fas fa-check-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Function to load and display job results in original format
// async function loadAndDisplayJobResults(jobId) {
//     console.log("DEBUG!!!! in load and display job results");
//     try {
//         const response = await window.pathUtils.fetch(`/fastq-job/${jobId}/results`);

//         if (response.ok) {
//             const fullResultsData = await response.json();
//             console.log('Full results loaded for immediate display:', fullResultsData);

//             if (fullResultsData.results && fullResultsData.results.sequences) {
//                 // Store the results globally for the original display functions
//                 alignmentResults = fullResultsData.results;

//                 // Display results using individual display functions
//                 displaySummary(alignmentResults.summary, alignmentResults.parameters);
//                 displayDistributionCharts(alignmentResults.sequences);
//                 storeSequenceData(alignmentResults.sequences);

//                 // Show tabs and results count
//                 resultsTabs.style.display = 'block';
//                 resultsCount.style.display = 'block';

//                 displayDetailedResults(alignmentResults.sequences);
//                 displayMutationAnalysis(alignmentResults.mutation_analysis);

//                 // Show detailed results section
//                 detailedResults.style.display = 'block';
//             } else {
//                 console.log('No sequences found in results');
//                 alert('Job completed but no sequences were found in the results.');
//             }
//         } else {
//             console.error('Failed to load job results:', response.status, response.statusText);
//             alert('Job completed but failed to load results for display.');
//         }
//     } catch (error) {
//         console.error('Error loading job results:', error);
//         alert(`Job completed but error loading results: ${error.message}`);
//     }
// }

// Jobs list functionality
async function loadJobsList() {
    try {
        const response = await window.pathUtils.fetch('/fastq-jobs');
        const jobs = await response.json();

        if (response.ok) {
            displayJobsList(jobs);
        } else {
            document.getElementById('jobsListContainer').innerHTML = `
                <div class="alert alert-danger">
                    Failed to load jobs: ${jobs.detail || 'Unknown error'}
                </div>
            `;
        }
    } catch (error) {
        document.getElementById('jobsListContainer').innerHTML = `
            <div class="alert alert-danger">
                Error loading jobs: ${error.message}
            </div>
        `;
    }
}

function displayJobsList(jobs) {
    const container = document.getElementById('jobsListContainer');

    if (jobs.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <p>No FASTQ alignment jobs found</p>
                <p class="small">Submit your first alignment above to get started</p>
            </div>
        `;
        return;
    }

    // Sort jobs by creation date (newest first)
    jobs.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Job Name</th>
                        <th>Job ID</th>
                        <th>Status</th>
                        <th>Filename</th>
                        <th>Sequences</th>
                        <th>Created</th>
                        <th>Completed</th>
                    </tr>
                </thead>
                <tbody>
        `;

    jobs.forEach(job => {
        const statusBadge = getStatusBadge(job.status);
        const createdDate = new Date(job.created_at).toLocaleString();
        const completedDate = job.completed_at ? new Date(job.completed_at).toLocaleString() : '-';
        const shortJobId = job.job_id.substring(0, 8) + '...';

        // Highlight recent jobs (completed within last 5 minutes)
        const isRecent = job.completed_at &&
            (new Date() - new Date(job.completed_at)) < 5 * 60 * 1000;
        const rowClass = isRecent ? 'job-row table-success' : 'job-row';

        html += `
            <tr class="${rowClass}" onclick="viewJobDetails('${job.job_id}')" style="cursor: pointer;">
                <td>
                    <strong>${job.job_name || 'Unnamed Job'}</strong>
                    ${isRecent ? '<span class="badge bg-success ms-2">New</span>' : ''}
                </td>
                <td>
                    <code class="small">${shortJobId}</code>
                </td>
                <td>${statusBadge}</td>
                <td class="small">${job.filename || '-'}</td>
                <td class="text-center">${job.sequence_count || 0}</td>
                <td class="small">${createdDate}</td>
                <td class="small">${completedDate}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
        <p class="text-muted small mt-2">
            <i class="fas fa-info-circle"></i>
            Click on any row to view detailed job information and results
        </p>
    `;

    container.innerHTML = html;
}

function getStatusBadge(status) {
    const badges = {
        'pending': '<span class="badge bg-warning">Pending</span>',
        'running': '<span class="badge bg-info">Running</span>',
        'completed': '<span class="badge bg-success">Completed</span>',
        'failed': '<span class="badge bg-danger">Failed</span>'
    };
    return badges[status] || `<span class="badge bg-secondary">${status}</span>`;
}

function refreshJobsList() {
    document.getElementById('jobsListContainer').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Refreshing...</span>
            </div>
            <p class="mt-2">Refreshing jobs...</p>
        </div>
    `;
    loadJobsList();
}

function viewJobDetails(jobId) {
    // Navigate to job details page
    window.location.href = window.pathUtils.buildUrl(`/fastq-job-details?job_id=${jobId}`);
}

// Function to view past jobs
function viewPastJobs() {
    // Create and show modal with past jobs
    showPastJobsModal();
}

// Show past jobs in a modal
async function showPastJobsModal() {
    try {
        const response = await window.pathUtils.fetch('/fastq-jobs');
        const jobs = await response.json();

        const modalHtml = `
            <div class="modal fade" id="pastJobsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-history"></i> Past FASTQ Alignment Jobs
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${jobs.length === 0 ? `
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p>No past jobs found. Submit your first FASTQ alignment to see jobs here.</p>
                                </div>
                            ` : `
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Job Name</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Sequences</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${jobs.map(job => `
                                                <tr>
                                                    <td>
                                                        <strong>${job.job_name}</strong><br>
                                                        <small class="text-muted">${job.filename || 'N/A'}</small>
                                                    </td>
                                                    <td>
                                                        <span class="badge ${getStatusBadgeClass(job.status)}">
                                                            ${job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small>${formatDate(job.created_at)}</small>
                                                    </td>
                                                    <td>
                                                        ${job.sequence_count || 'N/A'}
                                                    </td>
                                                    <td>
                                                        ${job.status === 'completed' ? `
                                                            <button class="btn btn-sm btn-primary" onclick="viewJobResults('${job.job_id}')">
                                                                <i class="fas fa-eye"></i> View Results
                                                            </button>
                                                        ` : `
                                                            <button class="btn btn-sm btn-secondary" disabled>
                                                                <i class="fas fa-clock"></i> ${job.status}
                                                            </button>
                                                        `}
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('pastJobsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('pastJobsModal'));
        modal.show();

        // Clean up modal when hidden
        document.getElementById('pastJobsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

    } catch (error) {
        console.error('Error loading past jobs:', error);
        alert('Failed to load past jobs. Please try again.');
    }
}

// Helper function to get status badge class
function getStatusBadgeClass(status) {
    switch (status) {
        case 'completed': return 'bg-success';
        case 'failed': return 'bg-danger';
        case 'running': return 'bg-info';
        case 'pending': return 'bg-warning';
        default: return 'bg-secondary';
    }
}

// Helper function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}

// Function to view job results from modal
function viewJobResults(jobId) {
    console.log('Viewing job results for:', jobId);

    // Close modal first
    const modal = bootstrap.Modal.getInstance(document.getElementById('pastJobsModal'));
    if (modal) {
        modal.hide();
    }

    // Navigate to job details page
    setTimeout(() => {
        const redirectUrl = `fastq-job-details?job_id=${jobId}`;
        console.log('Redirecting from modal to:', redirectUrl);
        window.location.href = redirectUrl;
    }, 300);
}

// Initialize tooltips and load jobs list
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any Bootstrap tooltips if needed
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Load jobs list when page loads
    loadJobsList();
});
