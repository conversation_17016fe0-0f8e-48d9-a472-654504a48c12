<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sequence Alignment Tools</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .feature-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            margin-bottom: 3rem;
        }
        .feature-list {
            list-style: none;
            padding-left: 0;
        }
        .feature-list li {
            padding: 0.5rem 0;
        }
        .feature-list li i {
            color: #28a745;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 mb-4">
                        <i class="fas fa-dna"></i> NeoSeq: Sequence Tools
                    </h1>
                    <p class="lead">Comprehensive bioinformatics tools for sequence analysis and alignment</p>
                    <p class="mb-0">Choose from our suite of alignment tools designed for different use cases</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Main Features -->
        <div class="row g-4 mb-5">
            <!-- FASTQ File Alignment -->
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon text-primary">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <h3 class="card-title">FASTQ File Alignment</h3>
                        <p class="card-text text-muted mb-4">
                            Upload FASTQ files and align multiple sequences against a reference sequence.
                            Perfect for analyzing sequencing data with quality scores.
                        </p>

                        <ul class="feature-list text-start mb-4">
                            <li><i class="fas fa-check"></i> Batch processing of multiple sequences</li>
                            <li><i class="fas fa-check"></i> Quality score analysis</li>
                            <li><i class="fas fa-check"></i> Automatic reverse complement detection</li>
                            <li><i class="fas fa-check"></i> Summary statistics and reports</li>
                        </ul>

                        <a href="/fastq-alignment" class="btn btn-primary btn-lg">
                            <i class="fas fa-upload"></i> Start FASTQ Alignment
                        </a>
                    </div>
                </div>
            </div>

            <!-- Multiple Sequence Alignment -->
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon text-success">
                            <i class="fas fa-align-left"></i>
                        </div>
                        <h3 class="card-title">Sequence Alignment</h3>
                        <p class="card-text text-muted mb-4">
                            Align multiple sequences using pairwise or MUSCLE algorithms.
                            Perfect for phylogenetic analysis and conserved region identification.
                        </p>

                        <ul class="feature-list text-start mb-4">
                            <li><i class="fas fa-check"></i> Pairwise sequence alignment</li>
                            <li><i class="fas fa-check"></i> Multiple sequence alignment (MUSCLE)</li>
                            <li><i class="fas fa-check"></i> FASTA file upload support</li>
                            <li><i class="fas fa-check"></i> Conservation analysis</li>
                        </ul>

                        <a href="/sequence-alignment" class="btn btn-success btn-lg">
                            <i class="fas fa-align-left"></i> Start Sequence Alignment
                        </a>
                    </div>
                </div>
            </div>

            <!-- BLAST Protein Search -->
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon text-warning">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3 class="card-title">BLAST Protein Search</h3>
                        <p class="card-text text-muted mb-4">
                            Search protein sequences against the nr database using BLASTP.
                            Find homologous sequences and analyze protein similarity.
                        </p>

                        <ul class="feature-list text-start mb-4">
                            <li><i class="fas fa-check"></i> BLASTP against nr database</li>
                            <li><i class="fas fa-check"></i> Real-time job status tracking</li>
                            <li><i class="fas fa-check"></i> Detailed hit analysis</li>
                            <li><i class="fas fa-check"></i> E-value and score metrics</li>
                        </ul>

                        <a href="/blast-search" class="btn btn-warning btn-lg">
                            <i class="fas fa-search"></i> Start BLAST Search
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> About These Tools</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-cogs"></i> Algorithm</h6>
                                <p class="text-muted small">
                                    Uses BioPython's PairwiseAligner with global alignment strategy. 
                                    Configurable scoring parameters for optimal results.
                                </p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-search"></i> Mutation Detection</h6>
                                <p class="text-muted small">
                                    Automatically identifies substitutions, insertions, and deletions. 
                                    Provides detailed position information and visual highlighting.
                                </p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-chart-line"></i> Quality Metrics</h6>
                                <p class="text-muted small">
                                    Comprehensive quality analysis including Phred scores, 
                                    alignment scores, and statistical summaries.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-5 py-4 border-top">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        <i class="fas fa-dna"></i> Sequence Alignment Tools
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">
                        <i class="fas fa-code"></i> Powered by BioPython & FastAPI
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/path-utils.js"></script>
</body>
</html>
