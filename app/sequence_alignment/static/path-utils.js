/**
 * Path utilities for handling root_path in uvicorn deployments
 * This script automatically detects the root path and provides utilities
 * for building correct URLs for navigation and API calls
 */

class PathUtils {
    constructor() {
        this.rootPath = this.detectRootPath();
        this.initializePathHandling();
    }

    /**
     * Detect the root path from the current URL
     * This works by analyzing the current pathname and extracting the base path
     */
    detectRootPath() {
        const currentPath = window.location.pathname;

        // Check if we're running under a known ingress prefix
        // This handles nginx rewrite scenarios where the backend receives rewritten paths
        // but the browser URL still shows the original prefix
        if (currentPath.startsWith('/neoseq')) {
            return '/neoseq';
        }

        // Extract root path by finding the base path before known endpoints
        const knownEndpoints = ['/fastq-alignment', '/sequence-alignment', '/sequence-details', '/blast-search', '/health', '/align', '/align-two-sequences', '/submit-blast', '/blast-job'];

        for (const endpoint of knownEndpoints) {
            if (currentPath.endsWith(endpoint)) {
                return currentPath.substring(0, currentPath.length - endpoint.length);
            }
        }

        // If current path contains known patterns, extract root path
        const patterns = ['/fastq-alignment', '/sequence-alignment', '/sequence-details', '/blast-search'];
        for (const pattern of patterns) {
            const index = currentPath.indexOf(pattern);
            if (index > 0) {
                return currentPath.substring(0, index);
            }
        }

        // If we're at the root or a direct page, no root path
        if (currentPath === '/' || currentPath === '/fastq-alignment' || currentPath === '/sequence-alignment' || currentPath === '/blast-search') {
            return '';
        }
        // // 
        // // Fallback: check if we're in a subdirectory by looking for common patterns
        // const pathParts = currentPath.split('/').filter(part => part);
        // if (pathParts.length > 0) {
        //     // Common root path patterns for sequence alignment
        //     const commonRootPaths = ['neoseq', 'alignment', 'seq-alignment', 'sequence-alignment'];
        //     for (let i = 0; i < pathParts.length; i++) {
        //         if (commonRootPaths.includes(pathParts[i])) {
        //             return '/' + pathParts.slice(0, i + 1).join('/');
        //         }
        //     }
        // }
        
        return '';
    }

    /**
     * Build a URL with the correct root path
     */
    buildUrl(path) {
        // Remove leading slash from path if present
        const cleanPath = path.startsWith('/') ? path.substring(1) : path;
        
        if (this.rootPath) {
            return `${this.rootPath}/${cleanPath}`;
        }
        return `/${cleanPath}`;
    }

    /**
     * Build an API URL with the correct root path
     */
    buildApiUrl(endpoint) {
        return this.buildUrl(endpoint);
    }

    /**
     * Initialize path handling for the current page
     */
    initializePathHandling() {
        // Update all navigation links when DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.updateNavigationLinks());
        } else {
            this.updateNavigationLinks();
        }
    }

    /**
     * Update all navigation links to include root path
     */
    updateNavigationLinks() {
        // Update navigation links
        const navLinks = document.querySelectorAll('a[href^="/"]');
        navLinks.forEach(link => {
            const originalHref = link.getAttribute('href');
            if (originalHref && !originalHref.startsWith('http') && !originalHref.includes(this.rootPath)) {
                link.href = this.buildUrl(originalHref);
            }
        });

        // Update form actions if any
        const forms = document.querySelectorAll('form[action^="/"]');
        forms.forEach(form => {
            const originalAction = form.getAttribute('action');
            if (originalAction && !originalAction.includes(this.rootPath)) {
                form.action = this.buildUrl(originalAction);
            }
        });
    }

    /**
     * Make an API call with the correct root path
     */
    async fetch(endpoint, options = {}) {
        const url = this.buildApiUrl(endpoint);
        return fetch(url, options);
    }

    /**
     * Navigate to a page with the correct root path
     */
    navigate(path) {
        window.location.href = this.buildUrl(path);
    }

    /**
     * Get the current root path
     */
    getRootPath() {
        return this.rootPath;
    }

    /**
     * Log current configuration for debugging
     */
    debug() {
        console.log('PathUtils Configuration:', {
            currentPath: window.location.pathname,
            detectedRootPath: this.rootPath,
            isIngressPrefix: window.location.pathname.startsWith('/neoseq'),
            buildUrl_root: this.buildUrl('/'),
            buildUrl_fastq: this.buildUrl('/fastq-alignment'),
            buildUrl_twoSeq: this.buildUrl('/sequence-alignment'),
            buildApiUrl_align: this.buildApiUrl('/align')
        });
    }
}

// Create global instance
window.pathUtils = new PathUtils();

// Global helper function for backward compatibility
function getApiPath(endpoint) {
    return window.pathUtils.buildApiUrl(endpoint);
}

// Export for module usage if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PathUtils;
}
