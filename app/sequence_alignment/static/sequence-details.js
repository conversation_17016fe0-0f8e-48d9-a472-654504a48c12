// Global variables
let sequenceData = null;

// DOM elements
const loadingDiv = document.getElementById('loadingDiv');
const errorMessage = document.getElementById('errorMessage');
const sequenceContent = document.getElementById('sequenceContent');
const errorSequenceId = document.getElementById('errorSequenceId');

// Get sequence ID from URL parameters
function getSequenceIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const encodedId = urlParams.get('id');
    // Decode the URL-encoded sequence ID
    return encodedId ? decodeURIComponent(encodedId) : null;
}

// Load sequence details
async function loadSequenceDetails() {
    const sequenceId = getSequenceIdFromUrl();

    if (!sequenceId) {
        showError('No sequence ID provided');
        return;
    }

    // Show loading
    loadingDiv.style.display = 'block';
    errorMessage.style.display = 'none';
    sequenceContent.style.display = 'none';

    try {
        // Try to get data from session storage first
        const storedData = sessionStorage.getItem('sequenceData');
        if (storedData) {
            const sequenceMap = JSON.parse(storedData);
            const data = sequenceMap[sequenceId];

            if (data) {
                sequenceData = data;
                displaySequenceDetails(data);
                return;
            }
        }

        // Fallback to API call if no stored data
        const apiUrl = window.pathUtils.buildApiUrl(`sequence/${encodeURIComponent(sequenceId)}`);
        const response = await fetch(apiUrl);
        const data = await response.json();

        if (data.error) {
            // Show error message for feature under development
            errorSequenceId.textContent = sequenceId;
            showError(data.message);
            return;
        }

        sequenceData = data;
        displaySequenceDetails(data);

    } catch (error) {
        console.error('Error loading sequence details:', error);
        showError('Failed to load sequence details');
    } finally {
        loadingDiv.style.display = 'none';
    }
}

// Show error message
function showError(message) {
    errorMessage.style.display = 'block';
    sequenceContent.style.display = 'none';
}

// Display sequence details (for future implementation)
function displaySequenceDetails(data) {
    // Populate sequence information
    document.getElementById('sequenceId').textContent = data.id;
    document.getElementById('sequenceLength').textContent = data.length;
    document.getElementById('gcContent').textContent = data.gc_content || 'N/A';
    document.getElementById('reverseComplement').textContent = data.is_reverse_complement ? 'Yes' : 'No';
    
    // Populate alignment metrics
    document.getElementById('alignmentScore').textContent = data.alignment_score;
    document.getElementById('mutationCount').textContent = data.num_mutations;
    document.getElementById('insertionStatus').textContent = data.insertion ? 'Yes' : 'No';
    document.getElementById('deletionStatus').textContent = data.deletion ? 'Yes' : 'No';
    
    // Populate quality metrics
    document.getElementById('avgQuality').textContent = data.avg_quality;
    document.getElementById('minQuality').textContent = data.min_quality;
    
    // Display mutations
    displayMutations(data.mutations || []);
    
    // Display full sequence
    displayFullSequence(data.full_sequence || 'Sequence data not available');
    
    // Display alignment
    console.log('Alignment data:', data.alignment);
    displayAlignment(data.alignment);
    
    // Display quality chart
    displayQualityChart(data.quality_scores || []);
    
    // Show badges
    displayBadges(data);
    
    // Show content
    sequenceContent.style.display = 'block';
}

// Display mutations list
function displayMutations(mutations) {
    const mutationsList = document.getElementById('mutationsList');
    
    if (mutations.length === 0) {
        mutationsList.innerHTML = '<p class="text-success"><i class="fas fa-check"></i> No mutations found</p>';
        return;
    }
    
    const mutationsHtml = mutations.map(mut => 
        `<span class="badge bg-danger me-1 mb-1">${mut.mutation}</span>`
    ).join('');
    
    mutationsList.innerHTML = mutationsHtml;
}

// Display full sequence
function displayFullSequence(sequence) {
    const fullSequence = document.getElementById('fullSequence');
    
    // Format sequence with line breaks every 80 characters
    const formattedSequence = sequence.match(/.{1,80}/g)?.join('\n') || sequence;
    fullSequence.textContent = formattedSequence;
}

// Display alignment
function displayAlignment(alignment) {
    const alignmentDisplay = document.getElementById('alignmentDisplay');

    console.log('Received alignment data:', alignment);

    // Always show alignment, even if no mutations are found
    if (!alignment) {
        alignmentDisplay.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>No alignment data available</strong><br>
                The alignment information was not stored for this sequence.
            </div>
        `;
        return;
    }

    // Handle different alignment data structures
    let referenceSeq, querySeq;

    if (alignment.reference_aligned && alignment.query_aligned) {
        // If data is already in the expected format
        referenceSeq = alignment.reference_aligned;
        querySeq = alignment.query_aligned;
    } else if (alignment.aligned_seq1 && alignment.aligned_seq2) {
        // If data is in the format from the backend
        referenceSeq = alignment.aligned_seq1;
        querySeq = alignment.aligned_seq2;
    } else {
        alignmentDisplay.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Alignment format not recognized</strong><br>
                The alignment data is in an unexpected format and cannot be displayed.
            </div>
        `;
        return;
    }

    // Create highlighted alignment
    const highlightedAlignment = createHighlightedAlignment(
        referenceSeq,
        querySeq
    );

    // Format alignment with line breaks for readability
    const formattedRef = formatAlignmentWithLineBreaks(highlightedAlignment.reference);
    const formattedQuery = formatAlignmentWithLineBreaks(highlightedAlignment.query);

    // Check if there are any differences in the alignment
    const hasDifferences = referenceSeq !== querySeq ||
                          referenceSeq.includes('-') ||
                          querySeq.includes('-');

    let statusMessage = '';
    if (!hasDifferences) {
        statusMessage = `
            <div class="alert alert-success mb-3">
                <i class="fas fa-check-circle"></i>
                <strong>Perfect Match!</strong> The query sequence aligns perfectly with the reference sequence with no mutations, insertions, or deletions.
            </div>
        `;
    }

    const alignmentHtml = `
        ${statusMessage}
        <div class="alignment-container">
            <div class="alignment-sequence mb-2">
                <div class="d-flex align-items-center mb-1">
                    <small><strong>Reference:</strong></small>
                </div>
                <div class="alignment-text-container">
                    <code class="alignment-text">${formattedRef}</code>
                </div>
            </div>
            <div class="alignment-sequence mb-2">
                <div class="d-flex align-items-center mb-1">
                    <small><strong>Query:</strong></small>
                </div>
                <div class="alignment-text-container">
                    <code class="alignment-text">${formattedQuery}</code>
                </div>
            </div>
            <div class="alignment-legend mt-3">
                <small>
                    <span class="legend-item">
                        <span class="highlight-mutation">■</span> Mutations
                    </span>
                    <span class="legend-item ms-3">
                        <span class="highlight-deletion">■</span> Deletions
                    </span>
                    <span class="legend-item ms-3">
                        <span class="highlight-insertion">■</span> Insertions
                    </span>
                </small>
            </div>
        </div>
    `;

    alignmentDisplay.innerHTML = alignmentHtml;
}

// Create highlighted alignment with mutations, insertions, and deletions
function createHighlightedAlignment(refSeq, querySeq) {
    let highlightedRef = '';
    let highlightedQuery = '';

    for (let i = 0; i < Math.max(refSeq.length, querySeq.length); i++) {
        const refBase = refSeq[i] || '';
        const queryBase = querySeq[i] || '';

        if (refBase === '-' && queryBase !== '-') {
            // Insertion: gap in reference, base in query
            highlightedRef += `<span class="highlight-insertion">${refBase}</span>`;
            highlightedQuery += `<span class="highlight-insertion">${queryBase}</span>`;
        } else if (refBase !== '-' && queryBase === '-') {
            // Deletion: base in reference, gap in query
            highlightedRef += `<span class="highlight-deletion">${refBase}</span>`;
            highlightedQuery += `<span class="highlight-deletion">${queryBase}</span>`;
        } else if (refBase !== queryBase && refBase !== '-' && queryBase !== '-') {
            // Mutation: different bases
            highlightedRef += `<span class="highlight-mutation">${refBase}</span>`;
            highlightedQuery += `<span class="highlight-mutation">${queryBase}</span>`;
        } else {
            // Match: same bases or both gaps
            highlightedRef += refBase;
            highlightedQuery += queryBase;
        }
    }

    return {
        reference: highlightedRef,
        query: highlightedQuery
    };
}

// Format alignment with line breaks for better readability
function formatAlignmentWithLineBreaks(alignmentString) {
    // Insert line breaks every 80 characters, but preserve HTML tags
    const lineLength = 80;
    let result = '';
    let currentLineLength = 0;
    let inTag = false;

    for (let i = 0; i < alignmentString.length; i++) {
        const char = alignmentString[i];

        if (char === '<') {
            inTag = true;
        } else if (char === '>') {
            inTag = false;
        }

        result += char;

        if (!inTag) {
            currentLineLength++;
            if (currentLineLength >= lineLength && char !== '<') {
                result += '\n';
                currentLineLength = 0;
            }
        }
    }

    return result;
}

// Display quality chart (placeholder)
function displayQualityChart(qualityScores) {
    const qualityChart = document.getElementById('qualityChart');
    
    if (qualityScores.length === 0) {
        qualityChart.innerHTML = '<p class="text-muted">No quality data available</p>';
        return;
    }
    
    // Simple quality visualization
    const avgQuality = qualityScores.reduce((a, b) => a + b, 0) / qualityScores.length;
    const minQuality = Math.min(...qualityScores);
    const maxQuality = Math.max(...qualityScores);
    
    qualityChart.innerHTML = `
        <div class="quality-summary">
            <p><strong>Average Quality:</strong> ${avgQuality.toFixed(2)}</p>
            <p><strong>Range:</strong> ${minQuality} - ${maxQuality}</p>
            <div class="quality-bar">
                <div class="quality-indicator" style="left: ${(avgQuality / 40) * 100}%"></div>
            </div>
            <small class="text-muted">Quality score visualization (simplified)</small>
        </div>
    `;
}

// Display badges
function displayBadges(data) {
    const badgesContainer = document.getElementById('sequenceBadges');
    let badges = '';
    
    if (data.is_reverse_complement) {
        badges += '<span class="badge bg-warning me-1">Reverse Complement</span>';
    }
    if (data.insertion) {
        badges += '<span class="badge bg-info me-1">Insertion</span>';
    }
    if (data.deletion) {
        badges += '<span class="badge bg-danger me-1">Deletion</span>';
    }
    if (data.num_mutations > 0) {
        badges += `<span class="badge bg-secondary me-1">${data.num_mutations} Mutations</span>`;
    }
    
    badgesContainer.innerHTML = badges;
}

// Copy sequence to clipboard
function copySequence(event) {
    if (!sequenceData || !sequenceData.full_sequence) {
        alert('No sequence data available to copy');
        return;
    }

    // Get the button element - handle cases where event might be undefined
    let btn;
    if (event && event.target) {
        btn = event.target.closest('button');
    } else {
        // Fallback: find the copy button by looking for the onclick attribute or specific selector
        btn = document.querySelector('button[onclick*="copySequence"]') ||
              document.querySelector('.btn:has(.fa-copy)') ||
              document.querySelector('button:contains("Copy Sequence")');
    }

    if (!btn) {
        // If we still can't find the button, create a temporary reference for visual feedback
        console.warn('Copy button not found, proceeding without visual feedback');
    }

    const originalText = btn ? btn.innerHTML : '';

    // Function to show success state
    function showSuccess() {
        if (btn) {
            btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-success');

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-primary');
            }, 2000);
        } else {
            // Show success notification if button not available
            showNotification('Sequence copied to clipboard!', 'success');
        }
    }

    // Function to show error state
    function showError(errorMessage) {
        console.error('Failed to copy sequence:', errorMessage);

        if (btn) {
            btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Copy Failed';
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-danger');

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-danger');
                btn.classList.add('btn-outline-primary');
            }, 3000);
        }

        // Try fallback method
        copyToClipboardFallback(sequenceData.full_sequence);
    }

    // Try modern clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(sequenceData.full_sequence)
            .then(showSuccess)
            .catch(err => showError(err));
    } else {
        // Use fallback method for older browsers or insecure contexts
        if (copyToClipboardFallback(sequenceData.full_sequence)) {
            showSuccess();
        } else {
            showError('Clipboard API not supported');
        }
    }
}

// Fallback clipboard copy method for older browsers or insecure contexts
function copyToClipboardFallback(text) {
    try {
        // Create a temporary textarea element
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        // Select and copy the text
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            // Show a user-friendly message since we can't update the button state here
            const notification = document.createElement('div');
            notification.className = 'alert alert-success position-fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '250px';
            notification.innerHTML = '<i class="fas fa-check"></i> Sequence copied to clipboard!';
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);

            return true;
        }
        return false;
    } catch (err) {
        console.error('Fallback copy failed:', err);

        // Last resort: show the sequence in a modal for manual copying
        showSequenceModal(text);
        return false;
    }
}

// Show notification message
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} position-fixed`;
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '250px';
    notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'info-circle'}"></i> ${message}`;
    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Show sequence in a modal for manual copying (last resort)
function showSequenceModal(sequence) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Copy Sequence Manually</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Please manually select and copy the sequence below:</p>
                    <textarea class="form-control" rows="10" readonly style="font-family: monospace;">${sequence}</textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // Clean up modal after it's hidden
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });

    // Auto-select the text in the textarea
    const textarea = modal.querySelector('textarea');
    textarea.focus();
    textarea.select();
}

// Download sequence as FASTA
function downloadSequence() {
    if (!sequenceData) {
        alert('No sequence data available to download');
        return;
    }
    
    const fastaContent = `>${sequenceData.id}\n${sequenceData.full_sequence}`;
    const blob = new Blob([fastaContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${sequenceData.id}.fasta`;
    link.click();
    
    URL.revokeObjectURL(url);
}

// Go back to previous page
function goBack() {
    if (document.referrer) {
        window.history.back();
    } else {
        // Use PathUtils to navigate to the correct root path
        if (window.pathUtils) {
            window.pathUtils.navigate('/');
        } else {
            window.location.href = '/';
        }
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // PathUtils is already initialized globally in path-utils.js
    // Just wait a bit to ensure it's fully loaded, then load sequence details
    setTimeout(loadSequenceDetails, 100);

    // Add event listener for copy button
    const copyBtn = document.getElementById('copySequenceBtn');
    if (copyBtn) {
        copyBtn.addEventListener('click', function(event) {
            copySequence(event);
        });
    }
});
