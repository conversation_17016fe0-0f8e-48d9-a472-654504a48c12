<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BLAST Job Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sequence-display {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            word-break: break-all;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        .status-pending { background-color: #ffc107; }
        .status-running { background-color: #17a2b8; }
        .status-completed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .hit-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .hit-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .alignment-display {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre;
            line-height: 1.4;
            border: 1px solid #e9ecef;
        }
        .alignment-display pre {
            margin: 0;
            font-size: 11px;
            line-height: 1.3;
            color: #333;
        }
        .bg-info.bg-opacity-10 {
            background-color: rgba(13, 110, 253, 0.1) !important;
        }
        .loading-container {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        #downloadDropdown {
            display: none; /* Initially hidden, shown when results are available */
        }
        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }
        .hit-card .badge {
            font-size: 0.7em;
        }
        .alignment-display pre {
            margin: 0;
            font-size: 10px;
            line-height: 1.2;
        }
        .card.bg-light {
            border: 1px solid #e9ecef;
        }
        .btn-sm {
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-info-circle text-primary"></i> BLAST Job Details</h1>
                        <p class="text-muted">Detailed information and results for BLAST search job</p>
                    </div>
                    <div>
                        <a href="/blast-search" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left"></i> Back to BLAST Search
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loadingContainer" class="loading-container">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading job details...</span>
                </div>
                <p class="mt-3">Loading job details...</p>
            </div>
        </div>

        <!-- Error State -->
        <div id="errorContainer" class="d-none">
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-triangle"></i> Error</h4>
                <p id="errorMessage">Failed to load job details</p>
                <button class="btn btn-outline-danger" onclick="loadJobDetails()">
                    <i class="fas fa-retry"></i> Retry
                </button>
            </div>
        </div>

        <!-- Job Details Content -->
        <div id="jobDetailsContainer" class="d-none">
            <!-- Job Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> Job Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Job Name:</strong> <span id="jobName">-</span></p>
                                    <p><strong>Job ID:</strong> <code id="jobId">-</code></p>
                                    <p><strong>Status:</strong> <span id="jobStatus">-</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Created:</strong> <span id="jobCreated">-</span></p>
                                    <p><strong>Completed:</strong> <span id="jobCompleted">-</span></p>
                                    <p><strong>Duration:</strong> <span id="jobDuration">-</span></p>
                                </div>
                            </div>
                            <div id="jobError" class="d-none">
                                <div class="alert alert-danger mt-3">
                                    <strong>Error:</strong> <span id="jobErrorMessage">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Query Sequence -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-dna"></i> Query Sequence</h5>
                        </div>
                        <div class="card-body">
                            <div class="sequence-display" id="querySequence">-</div>
                            <p class="text-muted small mt-2">
                                <strong>Length:</strong> <span id="sequenceLength">-</span> amino acids
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Section for Failed Jobs -->
            <div id="jobErrorContainer" class="d-none">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h5><i class="fas fa-exclamation-triangle"></i> Job Failed</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-danger">
                                    <h6>Error Details:</h6>
                                    <p id="jobErrorMessage" class="mb-0">-</p>
                                </div>
                                <div class="mt-3">
                                    <p class="text-muted">
                                        <strong>What you can do:</strong>
                                    </p>
                                    <ul class="text-muted">
                                        <li>Check if your protein sequence is valid</li>
                                        <li>Try submitting the job again</li>
                                        <li>Contact support if the problem persists</li>
                                    </ul>
                                </div>
                                <div class="mt-3">
                                    <a href="/blast-search" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Submit New Search
                                    </a>
                                    <button class="btn btn-outline-secondary" onclick="loadJobDetails()">
                                        <i class="fas fa-refresh"></i> Refresh Status
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div id="resultsContainer" class="d-none">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-chart-bar"></i> BLAST Results</h5>
                                <div class="dropdown">
                                    <button class="btn btn-success btn-sm dropdown-toggle" type="button" id="downloadDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-download"></i> Download Results
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="downloadDropdown">
                                        <li><a class="dropdown-item" href="#" onclick="downloadResults('json')">
                                            <i class="fas fa-file-code"></i> JSON Format
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="downloadResults('csv')">
                                            <i class="fas fa-file-csv"></i> CSV Format
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="downloadResults('tsv')">
                                            <i class="fas fa-file-alt"></i> TSV Format
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="downloadResults('xml')">
                                            <i class="fas fa-file-code"></i> XML Format
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <h3 class="text-primary" id="totalHits">-</h3>
                                            <p class="text-muted">Total Hits</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <h3 class="text-success" id="bestEvalue">-</h3>
                                            <p class="text-muted">Best E-value</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <h3 class="text-info" id="bestScore">-</h3>
                                            <p class="text-muted">Best Bit Score</p>
                                        </div>
                                    </div>
                                </div>
                                <div id="hitsContainer"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No Results -->
            <div id="noResultsContainer" class="d-none">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h4><i class="fas fa-info-circle"></i> No Results Found</h4>
                                    <p>No significant hits were found for your protein sequence with the current E-value threshold.</p>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-outline-success btn-sm dropdown-toggle" type="button" id="downloadDropdownNoResults" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-download"></i> Download Job Info
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="downloadDropdownNoResults">
                                        <li><a class="dropdown-item" href="#" onclick="downloadResults('json')">
                                            <i class="fas fa-file-code"></i> JSON Format
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="downloadResults('xml')">
                                            <i class="fas fa-file-code"></i> XML Format
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/path-utils.js"></script>
    <script>
        let currentJobId = null;
        let pollInterval = null;

        // Get job ID from URL parameters
        function getJobIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('job_id');
        }

        // Load job details
        async function loadJobDetails() {
            currentJobId = getJobIdFromUrl();
            
            if (!currentJobId) {
                showError('No job ID provided in URL');
                return;
            }

            showLoading();

            try {
                const response = await fetch(getApiPath(`/blast-job/${currentJobId}`));
                const jobData = await response.json();

                if (response.ok) {
                    displayJobDetails(jobData);
                    
                    // Start polling if job is still running
                    if (jobData.status === 'pending' || jobData.status === 'running') {
                        startPolling();
                    }
                } else {
                    showError(jobData.detail || 'Failed to load job details');
                }
            } catch (error) {
                showError(`Error loading job details: ${error.message}`);
            }
        }

        function showLoading() {
            document.getElementById('loadingContainer').classList.remove('d-none');
            document.getElementById('errorContainer').classList.add('d-none');
            document.getElementById('jobDetailsContainer').classList.add('d-none');
        }

        function showError(message) {
            document.getElementById('loadingContainer').classList.add('d-none');
            document.getElementById('errorContainer').classList.remove('d-none');
            document.getElementById('jobDetailsContainer').classList.add('d-none');
            document.getElementById('errorMessage').textContent = message;
        }

        function displayJobDetails(jobData) {
            console.log('Displaying job details:', jobData);
            document.getElementById('loadingContainer').classList.add('d-none');
            document.getElementById('errorContainer').classList.add('d-none');
            document.getElementById('jobDetailsContainer').classList.remove('d-none');

            // Job information
            document.getElementById('jobName').textContent = jobData.job_name || 'Unnamed Job';
            document.getElementById('jobId').textContent = jobData.job_id;
            document.getElementById('jobStatus').innerHTML = getStatusBadge(jobData.status);
            document.getElementById('jobCreated').textContent = new Date(jobData.created_at).toLocaleString();
            
            if (jobData.completed_at) {
                document.getElementById('jobCompleted').textContent = new Date(jobData.completed_at).toLocaleString();
                const duration = Math.round((new Date(jobData.completed_at) - new Date(jobData.created_at)) / 1000);
                document.getElementById('jobDuration').textContent = `${duration} seconds`;
            } else {
                document.getElementById('jobCompleted').textContent = '-';
                document.getElementById('jobDuration').textContent = '-';
            }

            // Handle failed jobs
            if (jobData.status === 'failed') {
                document.getElementById('jobErrorContainer').classList.remove('d-none');
                document.getElementById('resultsContainer').classList.add('d-none');
                document.getElementById('noResultsContainer').classList.add('d-none');

                // Display error message
                const errorMessage = jobData.error || 'Unknown error occurred during BLAST search';
                document.getElementById('jobErrorMessage').textContent = errorMessage;

                console.log('Job failed with error:', errorMessage);
                return; // Don't process results for failed jobs
            } else {
                // Hide error container for non-failed jobs
                document.getElementById('jobErrorContainer').classList.add('d-none');
            }

            // Query sequence
            if (jobData.sequence) {
                document.getElementById('querySequence').textContent = jobData.sequence;
                document.getElementById('sequenceLength').textContent = jobData.sequence_length || jobData.sequence.length;
            } else {
                document.getElementById('querySequence').textContent = 'Sequence not available';
                document.getElementById('sequenceLength').textContent = 'Unknown';
            }

            // Results handling - support both file-based and memory-based storage
            console.log('Processing results. Job status:', jobData.status);
            console.log('Job result object:', jobData.result);

            if (jobData.result) {
                if (jobData.result.stored_in_file) {
                    // Results are stored in file - fetch full results
                    console.log('Results stored in file, fetching full results...');
                    console.log('File path:', jobData.result.file_path);
                    console.log('Summary:', jobData.result.summary);
                    loadFullResults(jobData.job_id);
                } else if (jobData.result.results) {
                    // Results are in memory (backward compatibility)
                    console.log('Results found in memory, displaying directly');
                    displayResults(jobData.result.results);
                } else if (jobData.status === 'completed') {
                    // Completed but no results structure found
                    console.log('Job completed but no recognizable results structure');
                    document.getElementById('noResultsContainer').classList.remove('d-none');
                    showDownloadButton();
                }
            } else if (jobData.status === 'completed') {
                // No result object but job is completed
                console.log('Job completed but no result object found');
                document.getElementById('noResultsContainer').classList.remove('d-none');
                showDownloadButton();
            }
        }

        // Load full results from file storage
        async function loadFullResults(jobId) {
            try {
                console.log(`Loading full results for job ${jobId}...`);
                const response = await fetch(getApiPath(`/blast-job/${jobId}/results`));

                if (response.ok) {
                    const fullResultsData = await response.json();
                    console.log('Full results loaded:', fullResultsData);

                    if (fullResultsData.results && fullResultsData.results.hits) {
                        displayResults(fullResultsData.results);
                    } else {
                        console.log('No hits found in full results');
                        document.getElementById('noResultsContainer').classList.remove('d-none');
                        showDownloadButton();
                    }
                } else {
                    console.error('Failed to load full results:', response.status, response.statusText);
                    document.getElementById('noResultsContainer').classList.remove('d-none');
                    document.getElementById('noResultsContainer').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Failed to load detailed results. You can still download the results using the download button.
                        </div>
                    `;
                    showDownloadButton();
                }
            } catch (error) {
                console.error('Error loading full results:', error);
                document.getElementById('noResultsContainer').classList.remove('d-none');
                document.getElementById('noResultsContainer').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error loading detailed results: ${error.message}
                    </div>
                `;
                showDownloadButton();
            }
        }

        function showDownloadButton() {
            // Show download button for no results case
            const downloadBtnNoResults = document.getElementById('downloadDropdownNoResults');
            if (downloadBtnNoResults) {
                downloadBtnNoResults.style.display = 'block';
            }

            // Also show main download button
            const downloadBtn = document.getElementById('downloadDropdown');
            if (downloadBtn) {
                downloadBtn.style.display = 'block';
            }
        }

        function displayResults(results) {
            console.log('displayResults called with:', results);
            console.log('Number of hits:', results.hits ? results.hits.length : 'No hits array');

            document.getElementById('resultsContainer').classList.remove('d-none');

            // Show download button when results are available
            const downloadBtn = document.getElementById('downloadDropdown');
            if (downloadBtn) {
                downloadBtn.style.display = 'block';
            }

            // Summary statistics
            document.getElementById('totalHits').textContent = results.hits ? results.hits.length : 0;

            if (results.hits && results.hits.length > 0) {
                const bestHit = results.hits[0];
                const bestHsp = bestHit.hsps[0];

                document.getElementById('bestEvalue').textContent = bestHsp.evalue.toExponential(2);
                document.getElementById('bestScore').textContent = bestHsp.bit_score.toFixed(1);

                // Display hits
                displayHits(results.hits, results);
            } else {
                document.getElementById('bestEvalue').textContent = '-';
                document.getElementById('bestScore').textContent = '-';
                document.getElementById('noResultsContainer').classList.remove('d-none');
            }
        }

        function displayHits(hits, results = null) {
            const container = document.getElementById('hitsContainer');
            let html = '<h6>Top Hits:</h6>';
            
            hits.slice(0, 10).forEach((hit, index) => {
                const bestHsp = hit.hsps[0];
                const identity = bestHsp ? Math.round((bestHsp.identity / bestHsp.align_len) * 100) : 0;
                
                html += `
                    <div class="hit-card card">
                        <div class="hit-header card-header py-2">
                            <div class="row">
                                <div class="col-md-8">
                                    <strong>Hit ${index + 1}:</strong> <code>${hit.id}</code>
                                    ${hit.accession ? `<span class="badge bg-secondary ms-2">${hit.accession}</span>` : ''}
                                </div>
                                <div class="col-md-4 text-end">
                                    <small class="text-muted">Length: ${hit.length} aa</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body py-2">
                            <p class="mb-2"><strong>Description:</strong> ${hit.description}</p>
                            ${hit.organism ? `<p class="mb-2"><strong>Organism:</strong> <em>${hit.organism}</em></p>` : ''}

                            <!-- Primary Statistics -->
                            <div class="row mb-2">
                                <div class="col-md-3">
                                    <small><strong>E-value:</strong> ${bestHsp ? bestHsp.evalue.toExponential(2) : 'N/A'}</small>
                                </div>
                                <div class="col-md-3">
                                    <small><strong>Bit Score:</strong> ${bestHsp ? bestHsp.bit_score.toFixed(1) : 'N/A'}</small>
                                </div>
                                <div class="col-md-3">
                                    <small><strong>Identity:</strong> ${bestHsp && bestHsp.identity_percent ? bestHsp.identity_percent : (identity || 'N/A')}%</small>
                                </div>
                                <div class="col-md-3">
                                    <small><strong>Coverage:</strong> ${bestHsp ? Math.round((bestHsp.align_len / hit.length) * 100) : 0}%</small>
                                </div>
                            </div>

                            <!-- Additional Statistics -->
                            ${bestHsp ? `
                            <div class="row mb-2">
                                <div class="col-md-3">
                                    <small><strong>Positives:</strong> ${bestHsp.positive_percent ? bestHsp.positive_percent : (bestHsp.positive && bestHsp.align_len ? Math.round((bestHsp.positive / bestHsp.align_len) * 100) : 'N/A')}%</small>
                                </div>
                                <div class="col-md-3">
                                    <small><strong>Gaps:</strong> ${bestHsp.gaps_percent ? bestHsp.gaps_percent : (bestHsp.gaps && bestHsp.align_len ? Math.round((bestHsp.gaps / bestHsp.align_len) * 100) : 0)}%</small>
                                </div>
                                <div class="col-md-3">
                                    <small><strong>Query Range:</strong> ${bestHsp.query_from || 'N/A'}-${bestHsp.query_to || 'N/A'}</small>
                                </div>
                                <div class="col-md-3">
                                    <small><strong>Subject Range:</strong> ${bestHsp.hit_from || 'N/A'}-${bestHsp.hit_to || 'N/A'}</small>
                                </div>
                            </div>
                            ` : ''}
                            ${bestHsp && bestHsp.qseq ? `
                                <div class="mt-2">
                                    <div class="d-flex">
                                        <button class="btn btn-sm btn-outline-primary me-2" onclick="toggleAlignment(${index})">
                                            <i class="fas fa-align-left"></i> Show Alignment
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleDetails(${index})">
                                            <i class="fas fa-info-circle"></i> More Details
                                        </button>
                                    </div>

                                    <!-- Detailed Information (Hidden by Default) -->
                                    <div id="details-${index}" class="d-none mt-2">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <h6>Detailed Information</h6>
                                                <div class="mt-2">
                                                    <a href="https://www.ncbi.nlm.nih.gov/protein/${hit.accession || hit.id}" target="_blank" class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-external-link-alt"></i> View in NCBI
                                                    </a>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <small><strong>Accession:</strong> ${hit.accession || hit.id}</small><br>
                                                        <small><strong>Alignment Length:</strong> ${bestHsp.align_len || 'N/A'} aa</small><br>
                                                        <small><strong>Identical Residues:</strong> ${bestHsp.identity || 'N/A'} / ${bestHsp.align_len || 'N/A'}</small><br>
                                                        <small><strong>Positive Matches:</strong> ${bestHsp.positive || 'N/A'} / ${bestHsp.align_len || 'N/A'}</small><br>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <small><strong>Gaps:</strong> ${bestHsp.gaps || 0} / ${bestHsp.align_len || 'N/A'}</small><br>
                                                        <small><strong>Query Coverage:</strong> ${results && results.query_length && bestHsp.query_from && bestHsp.query_to ? Math.round(((bestHsp.query_to - bestHsp.query_from + 1) / results.query_length) * 100) : 'N/A'}%</small><br>
                                                        <small><strong>Subject Coverage:</strong> ${bestHsp.hit_from && bestHsp.hit_to && hit.length ? Math.round(((bestHsp.hit_to - bestHsp.hit_from + 1) / hit.length) * 100) : 'N/A'}%</small><br>
                                                        <small><strong>Raw Score:</strong> ${bestHsp.score || 'N/A'}</small><br>
                                                    </div>
                                                </div>

                                                
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Alignment Display -->
                                    <div id="alignment-${index}" class="d-none mt-2">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <h6>Sequence Alignment</h6>
                                                <div class="mb-2">
                                                    <small class="text-muted">
                                                        <strong>Midline symbols:</strong>
                                                        <code>Letter</code> = identical amino acid,
                                                        <code>+</code> = similar (chemically related),
                                                        <code>&nbsp;</code> = mismatch or gap
                                                    </small>
                                                    <div class="mt-1">
                                                        <small class="text-muted">
                                                            <strong>Examples:</strong>
                                                            <code>V</code> = both have V,
                                                            <code>+</code> = V↔I (hydrophobic), K↔R (positive),
                                                            <code>&nbsp;</code> space = no similarity
                                                        </small>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-info mt-1" onclick="toggleMidlineHelp(${index})">
                                                        <i class="fas fa-question-circle"></i> Detailed Explanation
                                                    </button>
                                                    <div id="midline-help-${index}" class="d-none mt-2 p-2 bg-info bg-opacity-10 rounded">
                                                        <small>
                                                            <strong>Chemical similarity groups:</strong><br>
                                                            • <strong>Hydrophobic:</strong> V, I, L, F, M, A (nonpolar)<br>
                                                            • <strong>Positive charge:</strong> K, R (basic)<br>
                                                            • <strong>Negative charge:</strong> D, E (acidic)<br>
                                                            • <strong>Polar:</strong> S, T, N, Q, Y (can form H-bonds)<br>
                                                            • <strong>Special:</strong> G (flexible), P (rigid), C (disulfide bonds)
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="alignment-display">
<pre>${formatAlignment(bestHsp)}</pre>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });
            
            if (hits.length > 10) {
                html += `<p class="text-muted"><em>Showing top 10 hits out of ${hits.length} total hits.</em></p>`;
            }
            
            container.innerHTML = html;
        }

        function toggleAlignment(index) {
            const alignmentDiv = document.getElementById(`alignment-${index}`);
            alignmentDiv.classList.toggle('d-none');
        }

        function toggleDetails(index) {
            const detailsDiv = document.getElementById(`details-${index}`);
            detailsDiv.classList.toggle('d-none');
        }

        function toggleMidlineHelp(index) {
            const helpDiv = document.getElementById(`midline-help-${index}`);
            helpDiv.classList.toggle('d-none');
        }

        function formatAlignment(hsp) {
            if (!hsp || !hsp.qseq || !hsp.hseq || !hsp.midline) {
                return 'Alignment data not available';
            }

            const queryFrom = hsp.query_from || '?';
            const queryTo = hsp.query_to || '?';
            const hitFrom = hsp.hit_from || '?';
            const hitTo = hsp.hit_to || '?';

            // Calculate padding to align the sequences properly
            const queryLabel = `Query: ${queryFrom}`;
            const hitLabel = `Hit:   ${hitFrom}`;
            const maxLabelLength = Math.max(queryLabel.length, hitLabel.length);

            // Pad labels to same length
            const paddedQueryLabel = queryLabel.padEnd(maxLabelLength + 1);
            const paddedHitLabel = hitLabel.padEnd(maxLabelLength + 1);
            const paddedMidLabel = ''.padEnd(maxLabelLength + 1);

            // Format the alignment with proper spacing
            const queryLine = `${paddedQueryLabel}${hsp.qseq} ${queryTo}`;
            const midLine = `${paddedMidLabel}${hsp.midline}`;
            const hitLine = `${paddedHitLabel}${hsp.hseq} ${hitTo}`;

            // Add a header line for better readability
            // const headerLine = `${''.padEnd(maxLabelLength + 1)}${''.padStart(10, '1234567890').repeat(Math.ceil(hsp.qseq.length / 10))}`;

            return `${queryLine}\n${midLine}\n${hitLine}`;
        }

        function getStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge bg-warning">Pending</span>',
                'running': '<span class="badge bg-info">Running</span>',
                'completed': '<span class="badge bg-success">Completed</span>',
                'failed': '<span class="badge bg-danger">Failed</span>'
            };
            return badges[status] || `<span class="badge bg-secondary">${status}</span>`;
        }

        function startPolling() {
            pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(getApiPath(`/blast-job/${currentJobId}`));
                    const jobData = await response.json();
                    
                    if (response.ok) {
                        displayJobDetails(jobData);
                        
                        if (jobData.status === 'completed' || jobData.status === 'failed') {
                            stopPolling();
                        }
                    }
                } catch (error) {
                    console.error('Error polling job status:', error);
                }
            }, 5000); // Poll every 5 seconds
        }

        function stopPolling() {
            if (pollInterval) {
                clearInterval(pollInterval);
                pollInterval = null;
            }
        }

        // Download results in specified format
        async function downloadResults(format) {
            if (!currentJobId) {
                alert('No job ID available');
                return;
            }

            try {
                // Show loading state
                const downloadBtn = document.getElementById('downloadDropdown');
                const originalText = downloadBtn.innerHTML;
                downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Preparing...';
                downloadBtn.disabled = true;

                // Create download URL
                const downloadUrl = getApiPath(`/blast-job/${currentJobId}/download?format=${format}`);

                // Create a temporary link and trigger download
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Reset button state
                setTimeout(() => {
                    downloadBtn.innerHTML = originalText;
                    downloadBtn.disabled = false;
                }, 1000);

            } catch (error) {
                console.error('Download error:', error);
                alert(`Failed to download results: ${error.message}`);

                // Reset button state
                const downloadBtn = document.getElementById('downloadDropdown');
                downloadBtn.innerHTML = '<i class="fas fa-download"></i> Download Results';
                downloadBtn.disabled = false;
            }
        }

        // Load job details when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadJobDetails();
        });

        // Clean up polling when page unloads
        window.addEventListener('beforeunload', function() {
            stopPolling();
        });
    </script>
</body>
</html>
