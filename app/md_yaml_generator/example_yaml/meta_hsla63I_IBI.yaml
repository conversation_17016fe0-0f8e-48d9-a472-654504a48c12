method: metadynamics
continue_md: false
steps: 300000000

integrator:
  dt: 0.002
  friction_coeff: 1.0
barostat:
  frequency: 25
  pressure: 1.0
temperature: 298
seed: 0

system_modify:
  7076:
    mass: 999999999
    
restraint:  
  restr_com:
    type: dist_ref_position    
    restr_grp:  '4,21,32,48,67,86,108,115,122,146,165,184,198,208,219,231,243,264,280,287,299,318,339,358,373,385,400,424,443,454,473,492,499,516,527,546,558,574,589,599,611,625,641,660,672,682,693,700,722,743,759,777,792,799,810,829,840,855,871,885,901,920,935,953,968,988,995,1002,1016,1030,1044,1055,1067,1081,1105,1119,1130,1140,1154,1170,1180,1190,1200,1220,1227,1234,1248,1262,1276,1295,1311,1323,1343,1354,1365,1382,1397,1412,1419,1436,1447,1466,1486,1497,1507,1526,1541,1553,1577,1594,1618,1640,1657,1668,1683,1707,1728,1743,1759,1778,1790,1806,1813,1833,1850,1867,1877,1896,1910,1922,1941,1952,1963,1978,1988,2012,2029,2044,2059,2078,2100,2124,2140,2156,2173,2185,2200,2207,2223,2237,2248,2269,2292,2311,2331,2348,2358,2379,2401,2408,2419,2435,2452,2468,2480,2492,2506,2520,2539,2559,2581,2597,2614,2631,2648,2658,2675,2690,2704,2711,2721,2740,2756,2773,2789,2805,2815,2830,2844,2851,2863,2879,2898,2910,2926,2945,2961,2985,3002,3012,3031,3041,3051,3058,3075,3089,3100,3114,3133,3157,3174,3184,3208,3222,3245,3259,3274,3293,3307,3322,3329,3344,3354,3368,3382,3406,3416,3435,3452,3471,3481,3498,3517,3527,3539,3548,3563,3582,3603,3619,3635,3651,3667,3678,3689,3711,3726,3736,3755,3766,3781,3797,3807,3822,3832,3856,3866,3888,3895,3919,3941,3957,3981,3991,4006,4020,4030,4045,4062,4083,4102,4122,4142,4154,4169,4180,4196,4215,4227,4243,4258,4272,4292,4307,4314,4321,4343,4367,4386,4407,4420,4434,4448,4463,4486,4501,4523,4538,4555,4572,4587,4604,4623,4647,4664,4674,4693,4704,4718,4725,4744,4763,4774,4790,4806,4817,4831,4843,4860,4870,4885,4905,4919,4939,4953,4960,4977,4999,5013,5032,5039,5063,5075,5087,5107,5118,5140,5158,5173,5187,5194,5200,5215,5222,5241,5256,5270,5294,5313,5330,5349,5366,5383,5398,5418,5425,5441,5465,5482,5489,5513,5533,5544,5561,5575,5599,5618,5634,5649,5668,5687,5697,5711,5724,5739,5749,5771,5790,5810,5817,5836,5856,5871,5893,5915,5922,5936,5955,5965,5981,5988,5999,6011,6021,6033,6052,6068,6084,6104,6115,6130,6144,6166,6183,6202,6216,6235,6246,6256,6266,6280,6297,6314,6325,6344,6363,6375,6396,6410,6429,6450,6465,6472,6493,6510,6526,6540,6547,6556,6571,6586,6605,6621,6638,6654,6678,6685,6709,6723,6742,6758,6775,6782,6794,6809,6828,6844,6854,6868,6883,6890,6900,6907,6921,6941,6960,6982,7006,7016,7040,7060'
    ref_position_nm: 4.30155, 4.30155, 4.30155
    max_nm: 0.5
    restr_k: 1000    
  restr_IBIC3C4_Z4DOH:
    type: distance
    grp1: '7081,7082'
    grp2: '7066,7067,7068,7069,7070,7071,7072,7073,7074,7075,7076,7077'
    restr_k: 1000
    min_nm: 0
    max_nm: 0.8
    order: 2
colvars:
  colvar1:
    # kcx_sidechain - Z4D+OH - IBI_N
    type: angle
    grp1_idx: '2267,2269,2271,2272,2273,2276,2279,2282,2285,2287,2288,2289'
    grp2_idx: '7066,7067,7068,7069,7070,7071,7072,7073,7074,7075,7076,7077'
    grp3_idx: '7078'
    min_cv_degree: 0
    max_cv_degree: 180
    bins: 181
    biasWidth_degree: 5
  colvar2:
    # Z4D_OH - IBI_N - IBI_C1
    type: angle
    grp1_idx: '7066,7067,7068,7069,7070,7071,7072,7073,7074,7075,7076,7077'
    grp2_idx: '7078'
    grp3_idx: '7079'
    min_cv_degree: 0
    max_cv_degree: 180
    bins: 181
    biasWidth_degree: 5
  colvar3:
    # center of mass between IBI-C3 and HOH
    type: distance
    grp1_idx: '7076'
    grp2_idx: '7081'
    min_cv_nm: 0
    max_cv_nm: 1.5
    bins: 250
    biasWidth_nm: 0.03
meta_set:
  biasFactor: 6    
  height: 0.3
  frequency: 10000
  

input_files:
  complex: /export/hanxinhao/project/meta_config/IBI_IBM/zn_dum4/mut63/min_hsla63I_IBI/last.pdbx
  system: /export/hanxinhao/project/meta_config/IBI_IBM/zn_dum4/mut63/sys_prep/hsla63I_IBI/system.xml
  ligands: /export/hanxinhao/project/meta_config/IBI_IBM/zn_dum4/mut63/sys_prep/hsla63I_IBI/ligand.json

output:
  output_dir: /export/hanxinhao/project/meta_config/IBI_IBM/zn_dum4/mut63/meta_hsla63I_IBI
  report_interval: 5000
  report_restraint: true
  trajectory_interval: 5000
  checkpoint_interval: 5000




