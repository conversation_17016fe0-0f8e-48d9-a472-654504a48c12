/* General Styles */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
    display: flex;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: #fff;
    padding: 20px;
    height: 100vh;
    position: sticky;
    top: 0;
}

.sidebar h2 {
    color: #fff;
    border-bottom: 1px solid #3498db;
    padding-bottom: 10px;
    margin-top: 0;
}

.example-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.example-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.example-btn:hover {
    background-color: #2980b9;
}

.container {
    flex: 1;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    overflow-y: auto;
    height: 100vh;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
}

h2 {
    color: #3498db;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-top: 30px;
}

h3 {
    color: #2980b9;
    margin-top: 20px;
}

/* Form Styles */
.section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

.form-group {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

label {
    display: inline-block;
    width: 300px;
    font-weight: bold;
}

input[type="text"],
input[type="number"],
select {
    width: 300px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

input[type="checkbox"] {
    width: auto;
}

/* Restraint Styles */
.restraint-item {
    background-color: #ecf0f1;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 5px;
    position: relative;
}

.remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-weight: bold;
}

/* Button Styles */
button {
    padding: 10px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

button[type="reset"] {
    background-color: #e74c3c;
}

button[type="reset"]:hover {
    background-color: #c0392b;
}

.add-shared-btn {
    background-color: #2ecc71;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
    transition: background-color 0.3s;
}
.add-shared-btn:hover {
    background-color: #27ae60;
}

.form-actions {
    margin-top: 30px;
    text-align: center;
}

/* YAML Output */
.yaml-output {
    margin-top: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

#yaml-result {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: monospace;
}

input#complex,
input#system,
input#ligands {
    width: 90%;
    min-width: 500px;
    max-width: 100%;
}

#output_dir {
    width: 90%;
    min-width: 500px;
    max-width: 100%;
}