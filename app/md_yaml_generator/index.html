<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neobinder MD YAML Generator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="sidebar">
        <h2>Example Configurations</h2>
        <div class="example-buttons">
            <button type="button" id="load-eq-example" class="example-btn">Equilibration Example</button>
            <button type="button" id="load-min-example" class="example-btn">Minimization Example</button>
            <button type="button" id="load-meta-example" class="example-btn">Metadynamics Example</button>
        </div>
    </div>
    <div class="container">
        <h1>Molecular Dynamics YAML Generator</h1>
        
        <form id="yamlForm">
            <div class="section">
                <h2>Basic Settings</h2>
                <div class="form-group">
                    <label for="method">Method:</label>
                    <select id="method" name="method" required>
                        <option value="eq">Equilibration (eq)</option>
                        <option value="min">Minimization (min)</option>
                        <option value="metadynamics">Metadynamics</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="continue_md">Continue MD:</label>
                    <input type="checkbox" id="continue_md" name="continue_md">
                </div>
                
                <div class="form-group">
                    <label for="steps">Steps:</label>
                    <input type="number" id="steps" name="steps" value="300000000" required>
                </div>
                
                <!-- Minimization Parameters (shown only when method is 'min') -->
                <div id="min-params-section" style="display: none;">
                    <h3>Minimization Parameters</h3>
                    <div class="form-group">
                        <label for="min_tolerance">Tolerance:</label>
                        <input type="number" id="min_tolerance" name="min_tolerance" value="1" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="min_maxiter">Max Iterations:</label>
                        <input type="number" id="min_maxiter" name="min_maxiter" value="100000">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="temperature">Temperature (K):</label>
                    <input type="number" id="temperature" name="temperature" value="298" required>
                </div>
                
                <div class="form-group">
                    <label for="seed">Seed:</label>
                    <input type="number" id="seed" name="seed" value="0" required>
                </div>
            </div>
            
            <div class="section">
                <h2>Integrator Settings</h2>
                <div class="form-group">
                    <label for="dt">Time Step (dt):</label>
                    <input type="number" id="dt" name="dt" value="0.002" step="0.001" required>
                </div>
                
                <div class="form-group">
                    <label for="friction_coeff">Friction Coefficient:</label>
                    <input type="number" id="friction_coeff" name="friction_coeff" value="1.0" step="0.1" required>
                </div>
            </div>
            
            <div class="section">
                <h2>Barostat Settings</h2>
                <div class="form-group">
                    <label for="barostat_frequency">Frequency:</label>
                    <input type="number" id="barostat_frequency" name="barostat_frequency" value="25" required>
                </div>
                
                <div class="form-group">
                    <label for="pressure">Pressure:</label>
                    <input type="number" id="pressure" name="pressure" value="1.0" step="0.1" required>
                </div>
            </div>
            
            <div class="section">
                <h2>Restraints</h2>
                <div class="form-group">
                    <label for="include_restraints">
                        <input type="checkbox" id="include_restraints" name="include_restraints" checked>
                        Include restraints in YAML
                    </label>
                </div>
                <div id="restraints-container">
                    <div class="restraint-item">
                        <h3>Restraint 1</h3>
                        <div class="form-group">
                            <label for="restr_name_1">Name:</label>
                            <input type="text" id="restr_name_1" name="restr_name_1" required>
                        </div>
                        <div class="form-group">
                            <label for="restr_type_1">Type:</label>
                            <select id="restr_type_1" name="restr_type_1" required>
                                <option value="sphere">Sphere</option>
                                <option value="funnel">Funnel</option>
                                <option value="distance">Distance</option>
                                <option value="angle">Angle</option>
                                <option value="dihedral">Dihedral</option>
                                <option value="ref_file">Reference File</option>
                                <option value="dist_ref_position">Distance Reference Position</option>
                                <option value="xyz_box">XYZ Box</option>
                                <option value="vec_restraint">Vector Restraint</option>
                                <option value="test">Test</option>
                            </select>
                        </div>
                        <div id="restraint_fields_1">
                            <div class="form-group">
                                <label for="restr_grp_1">Restraint Group:</label>
                                <input type="text" id="restr_grp_1" name="restr_grp_1">
                            </div>
                            <div class="form-group">
                                <label for="ref_position_nm_1">Reference Position (nm):</label>
                                <input type="text" id="ref_position_nm_1" name="ref_position_nm_1">
                            </div>
                            <div class="form-group">
                                <label for="max_nm_1">Max (nm):</label>
                                <input type="number" id="max_nm_1" name="max_nm_1" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="restr_k_1">Force Constant (restr_k):</label>
                                <input type="number" id="restr_k_1" name="restr_k_1">
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" id="add-restraint" class="add-shared-btn">Add Another Restraint</button>
            </div>
            
            <div class="section" id="colvars-section">
                <h2>Collective Variables (for Metadynamics)</h2>
                <div class="form-group">
                    <label for="include_colvars">
                        <input type="checkbox" id="include_colvars" name="include_colvars" checked>
                        Include collective variables in YAML
                    </label>
                </div>
                <div id="colvars-container">
                    <div class="colvar-item">
                        <h3>Collective Variable 1</h3>
                        <div class="form-group">
                            <label for="colvar_name_1">Name:</label>
                            <input type="text" id="colvar_name_1" name="colvar_name_1" required>
                        </div>
                        <div class="form-group">
                            <label for="colvar_type_1">Type:</label>
                            <select id="colvar_type_1" name="colvar_type_1" required>
                                <option value="distance">Distance</option>
                                <option value="dihedral">Dihedral</option>
                                <option value="angle">Angle</option>
                                <option value="min_distances">Min Distances</option>
                                <option value="distance_ref">Distance Reference</option>
                                <option value="rmsd">RMSD</option>
                            </select>
                        </div>
                        <div id="colvar_fields_1">
                            <div class="form-group">
                                <label for="grp1_idx_1">Group 1 Indices:</label>
                                <input type="text" id="grp1_idx_1" name="grp1_idx_1">
                            </div>
                            <div class="form-group">
                                <label for="grp2_idx_1">Group 2 Indices:</label>
                                <input type="text" id="grp2_idx_1" name="grp2_idx_1">
                            </div>
                            <div class="form-group">
                                <label for="min_cv_nm_1">Min CV (nm):</label>
                                <input type="number" id="min_cv_nm_1" name="min_cv_nm_1" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="max_cv_nm_1">Max CV (nm):</label>
                                <input type="number" id="max_cv_nm_1" name="max_cv_nm_1" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="bins_1">Bins:</label>
                                <input type="number" id="bins_1" name="bins_1" value="200">
                            </div>
                            <div class="form-group">
                                <label for="biasWidth_nm_1">Bias Width (nm):</label>
                                <input type="number" id="biasWidth_nm_1" name="biasWidth_nm_1" step="0.01" value="0.2">
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" id="add-colvar" class="add-shared-btn">Add Another Collective Variable</button>
            </div>
            
            <div class="section">
                <h2>Input Files</h2>
                <div class="form-group">
                    <label for="complex">Complex:</label>
                    <input type="text" id="complex" name="complex" size="100" value="/export/hanxinhao/project/cases/phytol/mechanism1/min1_wt_phytol/last.pdbx" required>
                </div>
                
                <div class="form-group">
                    <label for="system">System:</label>
                    <input type="text" id="system" name="system" size="100" value="/export/hanxinhao/project/cases/phytol/mechanism1/sys_prep/wt_phytol/system.xml" required>
                </div>
                
                <div class="form-group">
                    <label for="ligands">Ligands:</label>
                    <input type="text" id="ligands" name="ligands" size="100" value="/export/hanxinhao/project/cases/phytol/mechanism1/sys_prep/wt_phytol/ligand.json" required>
                </div>
            </div>
            
            <div class="section">
                <h2>Output Settings</h2>
                <div class="form-group">
                    <label for="output_dir">Output Directory:</label>
                    <input type="text" id="output_dir" name="output_dir" required>
                </div>
                
                <div class="form-group">
                    <label for="report_restraint">Report Restraint:</label>
                    <input type="checkbox" id="report_restraint" name="report_restraint" checked>
                </div>
                
                <div class="form-group">
                    <label for="report_interval">Report Interval:</label>
                    <input type="number" id="report_interval" name="report_interval" value="5000" required>
                </div>
                
                <div class="form-group">
                    <label for="trajectory_interval">Trajectory Interval:</label>
                    <input type="number" id="trajectory_interval" name="trajectory_interval" value="5000" required>
                </div>
                
                <div class="form-group">
                    <label for="checkpoint_interval">Checkpoint Interval:</label>
                    <input type="number" id="checkpoint_interval" name="checkpoint_interval" value="5000" required>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" id="generate-yaml">Generate YAML</button>
                <button type="button" id="download-yaml">Download YAML</button>
                <button type="reset">Reset Form</button>
            </div>
        </form>
        
        <div class="yaml-output">
            <h2>Generated YAML</h2>
            <pre id="yaml-result"></pre>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/js-yaml/4.1.0/js-yaml.min.js"></script>
    <script src="script.js"></script>
</body>
</html>