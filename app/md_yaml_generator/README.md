# MD YAML Generator

A simple web interface for generating YAML configuration files for molecular dynamics simulations.

## Features

- Configure basic simulation parameters (method, steps, temperature, etc.)
- Set up integrator and barostat settings
- Add and configure multiple restraints
- Specify input and output file paths
- Generate and download YAML configuration files

## Usage

1. Open `index.html` in a web browser
2. Fill in the form with your desired parameters
3. Click "Generate YAML" to see the YAML output
4. Click "Download YAML" to save the configuration file

## Available Methods

- **Equilibration (eq)**: For equilibrating your system
- **Minimization (min)**: For energy minimization
- **Metadynamics**: For enhanced sampling simulations

## Restraint Types

- **Distance Reference Position**: Restrains a group to a reference position
- **Distance**: Restrains the distance between two groups

## Example

The interface comes pre-populated with example values based on existing configuration files. You can modify these values as needed for your specific simulation.

## Requirements

- Modern web browser with JavaScript enabled
- Internet connection (for loading the js-yaml library from CDN)