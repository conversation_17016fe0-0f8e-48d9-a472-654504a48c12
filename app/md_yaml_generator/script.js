document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let restraintCounter = 1;
    let colvarCounter = 1;
    const yamlForm = document.getElementById('yamlForm');
    const generateYamlBtn = document.getElementById('generate-yaml');
    const downloadYamlBtn = document.getElementById('download-yaml');
    const addRestraintBtn = document.getElementById('add-restraint');
    const addColvarBtn = document.getElementById('add-colvar');
    const restraintsContainer = document.getElementById('restraints-container');
    const colvarsContainer = document.getElementById('colvars-container');
    const yamlResult = document.getElementById('yaml-result');
    
    // Example configuration buttons
    const loadEqExampleBtn = document.getElementById('load-eq-example');
    const loadMinExampleBtn = document.getElementById('load-min-example');
    const loadMetaExampleBtn = document.getElementById('load-meta-example');
    
    // Add event listeners
    generateYamlBtn.addEventListener('click', generateYaml);
    downloadYamlBtn.addEventListener('click', downloadYaml);
    addRestraintBtn.addEventListener('click', addRestraint);
    addColvarBtn.addEventListener('click', addColvar);
    
    // Add event listeners for example buttons
    loadEqExampleBtn.addEventListener('click', () => loadExampleConfig('example_yaml/eq1_wt_phytol.yaml'));
    loadMinExampleBtn.addEventListener('click', () => loadExampleConfig('example_yaml/min1_wt_phytol.yaml'));
    loadMetaExampleBtn.addEventListener('click', () => loadExampleConfig('example_yaml/meta_wt.yaml'));
    
    // Function to load example configurations
    function loadExampleConfig(filename) {
        fetch(filename)
            .then(response => response.text())
            .then(yamlText => {
                const yamlObj = jsyaml.load(yamlText);
                
                // Clear existing restraints except the first one
                const restraintItems = document.querySelectorAll('.restraint-item');
                for (let i = 1; i < restraintItems.length; i++) {
                    restraintsContainer.removeChild(restraintItems[i]);
                }
                restraintCounter = 1;
                
                // Set method and trigger change event to show/hide relevant fields
                document.getElementById('method').value = yamlObj.method;
                document.getElementById('method').dispatchEvent(new Event('change'));
                
                // Set basic settings
                if (yamlObj.method === 'min') {
                    // Minimization settings
                    if (yamlObj.min_params) {
                        document.getElementById('min_tolerance').value = yamlObj.min_params.tolerance || 1;
                        document.getElementById('min_maxiter').value = yamlObj.min_params.maxiter || 100000;
                    }
                } else {
                    // Non-minimization settings
                    document.getElementById('continue_md').checked = yamlObj.continue_md || false;
                    document.getElementById('steps').value = yamlObj.steps || 300000000;
                    
                    // Barostat settings
                    if (yamlObj.barostat) {
                        document.getElementById('barostat_frequency').value = yamlObj.barostat.frequency || 25;
                        document.getElementById('pressure').value = yamlObj.barostat.pressure || 1.0;
                    }
                }
                
                // Set common settings
                document.getElementById('temperature').value = yamlObj.temperature || 298;
                document.getElementById('seed').value = yamlObj.seed || 0;
                
                // Integrator settings
                if (yamlObj.integrator) {
                    document.getElementById('dt').value = yamlObj.integrator.dt || 0.002;
                    document.getElementById('friction_coeff').value = yamlObj.integrator.friction_coeff || 1.0;
                }
                
                // Input files
                if (yamlObj.input_files) {
                    document.getElementById('complex').value = yamlObj.input_files.complex || '';
                    document.getElementById('system').value = yamlObj.input_files.system || '';
                    document.getElementById('ligands').value = yamlObj.input_files.ligands || '';
                }
                
                // Output settings
                if (yamlObj.output) {
                    document.getElementById('output_dir').value = yamlObj.output.output_dir || '';
                    document.getElementById('report_restraint').checked = yamlObj.output.report_restraint !== undefined ? yamlObj.output.report_restraint : true;
                    document.getElementById('report_interval').value = yamlObj.output.report_interval || 5000;
                    document.getElementById('trajectory_interval').value = yamlObj.output.trajectory_interval || 5000;
                    document.getElementById('checkpoint_interval').value = yamlObj.output.checkpoint_interval || 5000;
                }
                
                // Handle restraints
                const includeRestraints = yamlObj.restraint && Object.keys(yamlObj.restraint).length > 0;
                document.getElementById('include_restraints').checked = includeRestraints;
                document.getElementById('include_restraints').dispatchEvent(new Event('change'));
                
                if (includeRestraints) {
                    // Set up first restraint
                    const restraintNames = Object.keys(yamlObj.restraint);
                    if (restraintNames.length > 0) {
                        const firstRestraintName = restraintNames[0];
                        const firstRestraint = yamlObj.restraint[firstRestraintName];
                        
                        document.getElementById('restr_name_1').value = firstRestraintName;
                        document.getElementById('restr_type_1').value = firstRestraint.type;
                        document.getElementById('restr_type_1').dispatchEvent(new Event('change'));
                        
                        // Set restraint fields based on type
                        if (firstRestraint.type === 'dist_ref_position') {
                            document.getElementById('restr_grp_1').value = firstRestraint.restr_grp || '';
                            document.getElementById('ref_position_nm_1').value = firstRestraint.ref_position_nm || '';
                            document.getElementById('max_nm_1').value = firstRestraint.max_nm || 0;
                            document.getElementById('restr_k_1').value = firstRestraint.restr_k || 0;
                        } else if (firstRestraint.type === 'distance') {
                            document.getElementById('grp1_1').value = firstRestraint.grp1 || '';
                            document.getElementById('grp2_1').value = firstRestraint.grp2 || '';
                            document.getElementById('min_nm_1').value = firstRestraint.min_nm || 0;
                            document.getElementById('max_nm_1').value = firstRestraint.max_nm || 0;
                            document.getElementById('restr_k_1').value = firstRestraint.restr_k || 0;
                            document.getElementById('order_1').value = firstRestraint.order || 2;
                        }
                        
                        // Add additional restraints
                        for (let i = 1; i < restraintNames.length; i++) {
                            addRestraint();
                            const restraintName = restraintNames[i];
                            const restraint = yamlObj.restraint[restraintName];
                            
                            document.getElementById(`restr_name_${i+1}`).value = restraintName;
                            document.getElementById(`restr_type_${i+1}`).value = restraint.type;
                            document.getElementById(`restr_type_${i+1}`).dispatchEvent(new Event('change'));
                            
                            if (restraint.type === 'dist_ref_position') {
                                document.getElementById(`restr_grp_${i+1}`).value = restraint.restr_grp || '';
                                document.getElementById(`ref_position_nm_${i+1}`).value = restraint.ref_position_nm || '';
                                document.getElementById(`max_nm_${i+1}`).value = restraint.max_nm || 0;
                                document.getElementById(`restr_k_${i+1}`).value = restraint.restr_k || 0;
                            } else if (restraint.type === 'distance') {
                                document.getElementById(`grp1_${i+1}`).value = restraint.grp1 || '';
                                document.getElementById(`grp2_${i+1}`).value = restraint.grp2 || '';
                                document.getElementById(`min_nm_${i+1}`).value = restraint.min_nm || 0;
                                document.getElementById(`max_nm_${i+1}`).value = restraint.max_nm || 0;
                                document.getElementById(`restr_k_${i+1}`).value = restraint.restr_k || 0;
                                document.getElementById(`order_${i+1}`).value = restraint.order || 2;
                            }
                        }
                        
                        // Ensure proper indexing of restraints
                        reindexRestraints();
                    }
                }
                
                // Handle collective variables for metadynamics
                if (yamlObj.method === 'metadynamics') {
                    // Clear existing colvars except the first one
                    const colvarItems = document.querySelectorAll('.colvar-item');
                    for (let i = 1; i < colvarItems.length; i++) {
                        colvarsContainer.removeChild(colvarItems[i]);
                    }
                    colvarCounter = 1;
                    
                    const includeColvars = yamlObj.colvars && Object.keys(yamlObj.colvars).length > 0;
                    document.getElementById('include_colvars').checked = includeColvars;
                    document.getElementById('include_colvars').dispatchEvent(new Event('change'));
                    
                    if (includeColvars) {
                        // Set up first colvar
                        const colvarNames = Object.keys(yamlObj.colvars);
                        if (colvarNames.length > 0) {
                            const firstColvarName = colvarNames[0];
                            const firstColvar = yamlObj.colvars[firstColvarName];
                            
                            document.getElementById('colvar_name_1').value = firstColvarName;
                            document.getElementById('colvar_type_1').value = firstColvar.type;
                            document.getElementById('colvar_type_1').dispatchEvent(new Event('change'));
                            
                            // Set colvar fields based on type
                            if (firstColvar.type === 'distance') {
                                document.getElementById('grp1_idx_1').value = firstColvar.grp1_idx || '';
                                document.getElementById('grp2_idx_1').value = firstColvar.grp2_idx || '';
                                document.getElementById('min_cv_nm_1').value = firstColvar.min_cv_nm || 0;
                                document.getElementById('max_cv_nm_1').value = firstColvar.max_cv_nm || 0;
                                document.getElementById('bins_1').value = firstColvar.bins || 200;
                                document.getElementById('biasWidth_nm_1').value = firstColvar.biasWidth_nm || 0.2;
                            } else if (firstColvar.type === 'dihedral') {
                                document.getElementById('grp1_idx_1').value = firstColvar.grp1_idx || '';
                                document.getElementById('grp2_idx_1').value = firstColvar.grp2_idx || '';
                                document.getElementById('grp3_idx_1').value = firstColvar.grp3_idx || '';
                                document.getElementById('grp4_idx_1').value = firstColvar.grp4_idx || '';
                                document.getElementById('min_cv_degree_1').value = firstColvar.min_cv_degree || 0;
                                document.getElementById('max_cv_degree_1').value = firstColvar.max_cv_degree || 0;
                                document.getElementById('bins_1').value = firstColvar.bins || 200;
                                document.getElementById('biasWidth_nm_1').value = firstColvar.biasWidth_degree || 0.2;
                            }
                            
                            // Add additional colvars
                            for (let i = 1; i < colvarNames.length; i++) {
                                addColvar();
                                const colvarName = colvarNames[i];
                                const colvar = yamlObj.colvars[colvarName];
                                
                                document.getElementById(`colvar_name_${i+1}`).value = colvarName;
                                document.getElementById(`colvar_type_${i+1}`).value = colvar.type;
                                document.getElementById(`colvar_type_${i+1}`).dispatchEvent(new Event('change'));
                                
                                if (colvar.type === 'distance') {
                                    document.getElementById(`grp1_idx_${i+1}`).value = colvar.grp1_idx || '';
                                    document.getElementById(`grp2_idx_${i+1}`).value = colvar.grp2_idx || '';
                                    document.getElementById(`min_cv_nm_${i+1}`).value = colvar.min_cv_nm || 0;
                                    document.getElementById(`max_cv_nm_${i+1}`).value = colvar.max_cv_nm || 0;
                                    document.getElementById(`bins_${i+1}`).value = colvar.bins || 200;
                                    document.getElementById(`biasWidth_nm_${i+1}`).value = colvar.biasWidth_nm || 0.2;
                                } else if (colvar.type === 'dihedral') {
                                    document.getElementById(`grp1_idx_${i+1}`).value = colvar.grp1_idx || '';
                                    document.getElementById(`grp2_idx_${i+1}`).value = colvar.grp2_idx || '';
                                    document.getElementById(`grp3_idx_${i+1}`).value = colvar.grp3_idx || '';
                                    document.getElementById(`grp4_idx_${i+1}`).value = colvar.grp4_idx || '';
                                    document.getElementById(`min_cv_degree_${i+1}`).value = colvar.min_cv_degree || 0;
                                    document.getElementById(`max_cv_degree_${i+1}`).value = colvar.max_cv_degree || 0;
                                    document.getElementById(`bins_${i+1}`).value = colvar.bins || 200;
                                    document.getElementById(`biasWidth_nm_${i+1}`).value = colvar.biasWidth_degree || 0.2;
                                }
                            }
                        }
                    }
                }
                
                // Generate YAML to show the result
                generateYaml();
            })
            .catch(error => {
                console.error('Error loading example configuration:', error);
                alert('Failed to load example configuration. Please try again.');
            });
    }
    
    // Add event listener to the first restraint type select
    document.getElementById('restr_type_1').addEventListener('change', function() {
        const fieldsContainer = document.getElementById('restraint_fields_1');
        fieldsContainer.innerHTML = createRestraintFields(1, this.value);
    });
    
    // Method change handler to show/hide relevant fields
    document.getElementById('method').addEventListener('change', function() {
        const methodValue = this.value;
        const stepsField = document.getElementById('steps').parentElement;
        const continueMdField = document.getElementById('continue_md').parentElement;
        const minParamsSection = document.getElementById('min-params-section');
        const colvarsSection = document.getElementById('colvars-section');
        
        // Show/hide fields based on the selected method
        if (methodValue === 'min') {
            // For minimization, hide steps and continue_md, show min params
            stepsField.style.display = 'none';
            continueMdField.style.display = 'none';
            minParamsSection.style.display = 'block';
            colvarsSection.style.display = 'none';
        } else if (methodValue === 'metadynamics') {
            // For metadynamics, show steps, continue_md, and colvars section
            stepsField.style.display = 'block';
            continueMdField.style.display = 'block';
            minParamsSection.style.display = 'none';
            colvarsSection.style.display = 'block';
        } else {
            // For other methods, show steps and continue_md, hide min params and colvars
            stepsField.style.display = 'block';
            continueMdField.style.display = 'block';
            minParamsSection.style.display = 'none';
            colvarsSection.style.display = 'none';
        }
    });
    
    // Colvars checkbox handler to show/hide colvars section
    document.getElementById('include_colvars').addEventListener('change', function() {
        const includeColvars = this.checked;
        const colvarsContainer = document.getElementById('colvars-container');
        colvarsContainer.style.display = includeColvars ? 'block' : 'none';
    });
    
    // Add event listener to the first colvar type select
    document.getElementById('colvar_type_1').addEventListener('change', function() {
        const fieldsContainer = document.getElementById('colvar_fields_1');
        fieldsContainer.innerHTML = createColvarFields(1, this.value);
    });
    

    // Function to reindex colvars after adding or removing
    function reindexColvars() {
        const colvarItems = document.querySelectorAll('.colvar-item');
        colvarCounter = colvarItems.length;
        
        // Update each colvar item with the correct index
        colvarItems.forEach((item, index) => {
            const newIndex = index + 1;
            
            // Update the heading
            const heading = item.querySelector('h3');
            if (heading) {
                heading.textContent = `Collective Variable ${newIndex}`;
            }
            
            // Update data-id attribute on remove button
            const removeBtn = item.querySelector('.remove-colvar');
            if (removeBtn) {
                removeBtn.setAttribute('data-id', newIndex);
            }
            
            // Update all input IDs, names, and labels
            const inputs = item.querySelectorAll('input, select');
            inputs.forEach(input => {
                const oldId = input.id;
                const baseName = oldId.split('_').slice(0, -1).join('_');
                const newId = `${baseName}_${newIndex}`;
                
                // Update input ID and name
                input.id = newId;
                input.name = newId;
                
                // Update associated label
                const label = item.querySelector(`label[for="${oldId}"]`);
                if (label) {
                    label.setAttribute('for', newId);
                }
            });
            
            // Update colvar fields container ID
            const fieldsContainer = item.querySelector('[id^="colvar_fields_"]');
            if (fieldsContainer) {
                fieldsContainer.id = `colvar_fields_${newIndex}`;
            }
            
            // Re-attach event listener to type select
            const typeSelect = item.querySelector(`[id^="colvar_type_"]`);
            if (typeSelect) {
                // Clone and replace to remove old event listeners
                const newTypeSelect = typeSelect.cloneNode(true);
                typeSelect.parentNode.replaceChild(newTypeSelect, typeSelect);
                
                // Add new event listener
                newTypeSelect.addEventListener('change', function() {
                    const fieldsContainer = document.getElementById(`colvar_fields_${newIndex}`);
                    fieldsContainer.innerHTML = createColvarFields(newIndex, this.value);
                });
            }
        });
    }
    
    // Function to add a new collective variable
    function addColvar() {
        colvarCounter++;
        
        const colvarItem = document.createElement('div');
        colvarItem.className = 'colvar-item';
        colvarItem.innerHTML = `
            <h3>Collective Variable ${colvarCounter}</h3>
            <div class="form-group">
                <label for="colvar_name_${colvarCounter}">Name:</label>
                <input type="text" id="colvar_name_${colvarCounter}" name="colvar_name_${colvarCounter}" required>
            </div>
            <div class="form-group">
                <label for="colvar_type_${colvarCounter}">Type:</label>
                <select id="colvar_type_${colvarCounter}" name="colvar_type_${colvarCounter}" required>
                    <option value="distance">Distance</option>
                    <option value="dihedral">Dihedral</option>
                    <option value="angle">Angle</option>
                    <option value="min_distances">Min Distances</option>
                    <option value="distance_ref">Distance Reference</option>
                    <option value="rmsd">RMSD</option>
                </select>
            </div>
            <div id="colvar_fields_${colvarCounter}">
                <div class="form-group">
                    <label for="grp1_idx_${colvarCounter}">Group 1 Indices:</label>
                    <input type="text" id="grp1_idx_${colvarCounter}" name="grp1_idx_${colvarCounter}">
                </div>
                <div class="form-group">
                    <label for="grp2_idx_${colvarCounter}">Group 2 Indices:</label>
                    <input type="text" id="grp2_idx_${colvarCounter}" name="grp2_idx_${colvarCounter}">
                </div>
                <div class="form-group">
                    <label for="min_cv_nm_${colvarCounter}">Min CV (nm):</label>
                    <input type="number" id="min_cv_nm_${colvarCounter}" name="min_cv_nm_${colvarCounter}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_cv_nm_${colvarCounter}">Max CV (nm):</label>
                    <input type="number" id="max_cv_nm_${colvarCounter}" name="max_cv_nm_${colvarCounter}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="bins_${colvarCounter}">Bins:</label>
                    <input type="number" id="bins_${colvarCounter}" name="bins_${colvarCounter}" value="200">
                </div>
                <div class="form-group">
                    <label for="biasWidth_nm_${colvarCounter}">Bias Width (nm):</label>
                    <input type="number" id="biasWidth_nm_${colvarCounter}" name="biasWidth_nm_${colvarCounter}" step="0.01" value="0.2">
                </div>
            </div>
            <button type="button" class="remove-colvar" data-id="${colvarCounter}">Remove</button>
        `;
        colvarsContainer.appendChild(colvarItem);
        document.getElementById(`colvar_type_${colvarCounter}`).addEventListener('change', function() {
            const fieldsContainer = document.getElementById(`colvar_fields_${colvarCounter}`);
            fieldsContainer.innerHTML = createColvarFields(colvarCounter, this.value);
        });
        colvarItem.querySelector('.remove-colvar').addEventListener('click', function() {
            const colvarToRemove = this.closest('.colvar-item');
            if (colvarToRemove) {
                colvarsContainer.removeChild(colvarToRemove);
                reindexColvars();
            }
        });
        reindexColvars();
    }
    
    // Function to create colvar fields based on type
    function createColvarFields(colvarId, type) {
        // Common fields used in most colvar types
        const binsField = `
            <div class="form-group">
                <label for="bins_${colvarId}">Bins:</label>
                <input type="number" id="bins_${colvarId}" name="bins_${colvarId}" value="200">
            </div>
        `;
        
        const biasWidthField = `
            <div class="form-group">
                <label for="biasWidth_nm_${colvarId}">Bias Width (nm):</label>
                <input type="number" id="biasWidth_nm_${colvarId}" name="biasWidth_nm_${colvarId}" step="0.01" value="0.2">
            </div>
        `;
        
        if (type === 'distance') {
            return `
                <div class="form-group">
                    <label for="grp1_idx_${colvarId}">Group 1 Indices:</label>
                    <input type="text" id="grp1_idx_${colvarId}" name="grp1_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="grp2_idx_${colvarId}">Group 2 Indices:</label>
                    <input type="text" id="grp2_idx_${colvarId}" name="grp2_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="min_cv_nm_${colvarId}">Min CV (nm):</label>
                    <input type="number" id="min_cv_nm_${colvarId}" name="min_cv_nm_${colvarId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_cv_nm_${colvarId}">Max CV (nm):</label>
                    <input type="number" id="max_cv_nm_${colvarId}" name="max_cv_nm_${colvarId}" step="0.01">
                </div>
                ${binsField}
                ${biasWidthField}
            `;
        } else if (type === 'dihedral') {
            return `
                <div class="form-group">
                    <label for="grp1_idx_${colvarId}">Group 1 Indices:</label>
                    <input type="text" id="grp1_idx_${colvarId}" name="grp1_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="grp2_idx_${colvarId}">Group 2 Indices:</label>
                    <input type="text" id="grp2_idx_${colvarId}" name="grp2_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="grp3_idx_${colvarId}">Group 3 Indices:</label>
                    <input type="text" id="grp3_idx_${colvarId}" name="grp3_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="grp4_idx_${colvarId}">Group 4 Indices:</label>
                    <input type="text" id="grp4_idx_${colvarId}" name="grp4_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="min_cv_degree_${colvarId}">Min CV (degree):</label>
                    <input type="number" id="min_cv_degree_${colvarId}" name="min_cv_degree_${colvarId}" step="0.1">
                </div>
                <div class="form-group">
                    <label for="max_cv_degree_${colvarId}">Max CV (degree):</label>
                    <input type="number" id="max_cv_degree_${colvarId}" name="max_cv_degree_${colvarId}" step="0.1">
                </div>
                ${binsField}
                ${biasWidthField}
            `;
        } else if (type === 'angle') {
            return `
                <div class="form-group">
                    <label for="grp1_idx_${colvarId}">Group 1 Indices:</label>
                    <input type="text" id="grp1_idx_${colvarId}" name="grp1_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="grp2_idx_${colvarId}">Group 2 Indices:</label>
                    <input type="text" id="grp2_idx_${colvarId}" name="grp2_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="grp3_idx_${colvarId}">Group 3 Indices:</label>
                    <input type="text" id="grp3_idx_${colvarId}" name="grp3_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="min_cv_degree_${colvarId}">Min CV (degree):</label>
                    <input type="number" id="min_cv_degree_${colvarId}" name="min_cv_degree_${colvarId}" step="0.1">
                </div>
                <div class="form-group">
                    <label for="max_cv_degree_${colvarId}">Max CV (degree):</label>
                    <input type="number" id="max_cv_degree_${colvarId}" name="max_cv_degree_${colvarId}" step="0.1">
                </div>
                ${binsField}
                ${biasWidthField}
            `;
        } else if (type === 'min_distances') {
            return `
                <div class="form-group">
                    <label for="grp1_idx_${colvarId}">Group 1 Indices:</label>
                    <input type="text" id="grp1_idx_${colvarId}" name="grp1_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="grp2_idx_${colvarId}">Group 2 Indices:</label>
                    <input type="text" id="grp2_idx_${colvarId}" name="grp2_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="min_cv_nm_${colvarId}">Min CV (nm):</label>
                    <input type="number" id="min_cv_nm_${colvarId}" name="min_cv_nm_${colvarId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_cv_nm_${colvarId}">Max CV (nm):</label>
                    <input type="number" id="max_cv_nm_${colvarId}" name="max_cv_nm_${colvarId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="num_pairs_${colvarId}">Number of Pairs:</label>
                    <input type="number" id="num_pairs_${colvarId}" name="num_pairs_${colvarId}" value="1">
                </div>
                ${binsField}
                ${biasWidthField}
            `;
        } else if (type === 'distance_ref') {
            return `
                <div class="form-group">
                    <label for="grp1_idx_${colvarId}">Group Indices:</label>
                    <input type="text" id="grp1_idx_${colvarId}" name="grp1_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="ref_position_nm_${colvarId}">Reference Position (nm):</label>
                    <input type="text" id="ref_position_nm_${colvarId}" name="ref_position_nm_${colvarId}" placeholder="x,y,z">
                </div>
                <div class="form-group">
                    <label for="min_cv_nm_${colvarId}">Min CV (nm):</label>
                    <input type="number" id="min_cv_nm_${colvarId}" name="min_cv_nm_${colvarId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_cv_nm_${colvarId}">Max CV (nm):</label>
                    <input type="number" id="max_cv_nm_${colvarId}" name="max_cv_nm_${colvarId}" step="0.01">
                </div>
                ${binsField}
                ${biasWidthField}
            `;
        } else if (type === 'rmsd') {
            return `
                <div class="form-group">
                    <label for="grp1_idx_${colvarId}">Group Indices:</label>
                    <input type="text" id="grp1_idx_${colvarId}" name="grp1_idx_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="ref_file_${colvarId}">Reference File:</label>
                    <input type="text" id="ref_file_${colvarId}" name="ref_file_${colvarId}">
                </div>
                <div class="form-group">
                    <label for="min_cv_nm_${colvarId}">Min CV (nm):</label>
                    <input type="number" id="min_cv_nm_${colvarId}" name="min_cv_nm_${colvarId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_cv_nm_${colvarId}">Max CV (nm):</label>
                    <input type="number" id="max_cv_nm_${colvarId}" name="max_cv_nm_${colvarId}" step="0.01">
                </div>
                ${binsField}
                ${biasWidthField}
            `;
        }
        
        // Default fields if type is not recognized
        return `
            <div class="form-group">
                <label for="grp1_idx_${colvarId}">Group 1 Indices:</label>
                <input type="text" id="grp1_idx_${colvarId}" name="grp1_idx_${colvarId}">
            </div>
            <div class="form-group">
                <label for="grp2_idx_${colvarId}">Group 2 Indices:</label>
                <input type="text" id="grp2_idx_${colvarId}" name="grp2_idx_${colvarId}">
            </div>
            <div class="form-group">
                <label for="min_cv_nm_${colvarId}">Min CV (nm):</label>
                <input type="number" id="min_cv_nm_${colvarId}" name="min_cv_nm_${colvarId}" step="0.01">
            </div>
            <div class="form-group">
                <label for="max_cv_nm_${colvarId}">Max CV (nm):</label>
                <input type="number" id="max_cv_nm_${colvarId}" name="max_cv_nm_${colvarId}" step="0.01">
            </div>
            ${binsField}
            ${biasWidthField}
        `;
    }
    
    // Trigger the change event to set initial visibility
    document.getElementById('method').dispatchEvent(new Event('change'));
    
    // Restraints checkbox handler to show/hide restraints section
    document.getElementById('include_restraints').addEventListener('change', function() {
        const includeRestraints = this.checked;
        const restraintsContainer = document.getElementById('restraints-container');
        restraintsContainer.style.display = includeRestraints ? 'block' : 'none';
    });
    
    // Function to create restraint fields based on type
    function createRestraintFields(restraintId, type) {
        // Common force constant field used in most restraint types
        const forceConstantField = `
            <div class="form-group">
                <label for="restr_k_${restraintId}">Force Constant (restr_k):</label>
                <input type="number" id="restr_k_${restraintId}" name="restr_k_${restraintId}">
            </div>
        `;
        
        // Common order parameter field used in several restraint types
        const orderField = `
            <div class="form-group">
                <label for="order_${restraintId}">Order:</label>
                <input type="number" id="order_${restraintId}" name="order_${restraintId}" value="2">
            </div>
        `;
        
        if (type === 'sphere') {
            return `
                <div class="form-group">
                    <label for="restr_grp_${restraintId}">Restraint Group:</label>
                    <input type="text" id="restr_grp_${restraintId}" name="restr_grp_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="cent_grp_${restraintId}">Center Group:</label>
                    <input type="text" id="cent_grp_${restraintId}" name="cent_grp_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="radius_nm_${restraintId}">Radius (nm):</label>
                    <input type="number" id="radius_nm_${restraintId}" name="radius_nm_${restraintId}" step="0.01">
                </div>
                ${forceConstantField}
            `;
        } else if (type === 'funnel') {
            return `
                <div class="form-group">
                    <label for="restr_grp_${restraintId}">Restraint Group:</label>
                    <input type="text" id="restr_grp_${restraintId}" name="restr_grp_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="gate_grp_${restraintId}">Gate Group:</label>
                    <input type="text" id="gate_grp_${restraintId}" name="gate_grp_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="pocket_grp_${restraintId}">Pocket Group:</label>
                    <input type="text" id="pocket_grp_${restraintId}" name="pocket_grp_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="upper_wall_nm_${restraintId}">Upper Wall (nm):</label>
                    <input type="number" id="upper_wall_nm_${restraintId}" name="upper_wall_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="lower_wall_nm_${restraintId}">Lower Wall (nm):</label>
                    <input type="number" id="lower_wall_nm_${restraintId}" name="lower_wall_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="width_${restraintId}">Width (nm):</label>
                    <input type="number" id="width_${restraintId}" name="width_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="buffer_${restraintId}">Buffer (nm):</label>
                    <input type="number" id="buffer_${restraintId}" name="buffer_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="steepness_${restraintId}">Steepness (nm):</label>
                    <input type="number" id="steepness_${restraintId}" name="steepness_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="s_center_${restraintId}">S Center (nm):</label>
                    <input type="number" id="s_center_${restraintId}" name="s_center_${restraintId}" step="0.01">
                </div>
                ${forceConstantField}
            `;
        } else if (type === 'distance') {
            return `
                <div class="form-group">
                    <label for="grp1_${restraintId}">Group 1:</label>
                    <input type="text" id="grp1_${restraintId}" name="grp1_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="grp2_${restraintId}">Group 2:</label>
                    <input type="text" id="grp2_${restraintId}" name="grp2_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="min_nm_${restraintId}">Min (nm):</label>
                    <input type="number" id="min_nm_${restraintId}" name="min_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_nm_${restraintId}">Max (nm):</label>
                    <input type="number" id="max_nm_${restraintId}" name="max_nm_${restraintId}" step="0.01">
                </div>
                ${forceConstantField}
                ${orderField}
            `;
        } else if (type === 'angle') {
            return `
                <div class="form-group">
                    <label for="grp1_${restraintId}">Group 1:</label>
                    <input type="text" id="grp1_${restraintId}" name="grp1_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="grp2_${restraintId}">Group 2:</label>
                    <input type="text" id="grp2_${restraintId}" name="grp2_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="grp3_${restraintId}">Group 3:</label>
                    <input type="text" id="grp3_${restraintId}" name="grp3_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="min_degree_${restraintId}">Min (degree):</label>
                    <input type="number" id="min_degree_${restraintId}" name="min_degree_${restraintId}" step="0.1">
                </div>
                <div class="form-group">
                    <label for="max_degree_${restraintId}">Max (degree):</label>
                    <input type="number" id="max_degree_${restraintId}" name="max_degree_${restraintId}" step="0.1">
                </div>
                ${forceConstantField}
                ${orderField}
            `;
        } else if (type === 'dihedral') {
            return `
                <div class="form-group">
                    <label for="grp1_${restraintId}">Group 1:</label>
                    <input type="text" id="grp1_${restraintId}" name="grp1_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="grp2_${restraintId}">Group 2:</label>
                    <input type="text" id="grp2_${restraintId}" name="grp2_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="grp3_${restraintId}">Group 3:</label>
                    <input type="text" id="grp3_${restraintId}" name="grp3_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="grp4_${restraintId}">Group 4:</label>
                    <input type="text" id="grp4_${restraintId}" name="grp4_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="min_degree_${restraintId}">Min (degree):</label>
                    <input type="number" id="min_degree_${restraintId}" name="min_degree_${restraintId}" step="0.1">
                </div>
                <div class="form-group">
                    <label for="max_degree_${restraintId}">Max (degree):</label>
                    <input type="number" id="max_degree_${restraintId}" name="max_degree_${restraintId}" step="0.1">
                </div>
                ${forceConstantField}
                ${orderField}
            `;
        } else if (type === 'ref_file') {
            return `
                <div class="form-group">
                    <label for="ref_file_${restraintId}">Reference File:</label>
                    <input type="text" id="ref_file_${restraintId}" name="ref_file_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="restr_grp_${restraintId}">Restraint Group:</label>
                    <input type="text" id="restr_grp_${restraintId}" name="restr_grp_${restraintId}">
                </div>
                ${forceConstantField}
            `;
        } else if (type === 'dist_ref_position') {
            return `
                <div class="form-group">
                    <label for="restr_grp_${restraintId}">Restraint Group:</label>
                    <input type="text" id="restr_grp_${restraintId}" name="restr_grp_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="ref_position_nm_${restraintId}">Reference Position (nm):</label>
                    <input type="text" id="ref_position_nm_${restraintId}" name="ref_position_nm_${restraintId}" placeholder="x,y,z">
                </div>
                <div class="form-group">
                    <label for="min_nm_${restraintId}">Min (nm):</label>
                    <input type="number" id="min_nm_${restraintId}" name="min_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_nm_${restraintId}">Max (nm):</label>
                    <input type="number" id="max_nm_${restraintId}" name="max_nm_${restraintId}" step="0.01">
                </div>
                ${forceConstantField}
                ${orderField}
            `;
        } else if (type === 'xyz_box') {
            return `
                <div class="form-group">
                    <label for="restr_grp_${restraintId}">Restraint Group:</label>
                    <input type="text" id="restr_grp_${restraintId}" name="restr_grp_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="min_x_nm_${restraintId}">Min X (nm):</label>
                    <input type="number" id="min_x_nm_${restraintId}" name="min_x_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_x_nm_${restraintId}">Max X (nm):</label>
                    <input type="number" id="max_x_nm_${restraintId}" name="max_x_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="min_y_nm_${restraintId}">Min Y (nm):</label>
                    <input type="number" id="min_y_nm_${restraintId}" name="min_y_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_y_nm_${restraintId}">Max Y (nm):</label>
                    <input type="number" id="max_y_nm_${restraintId}" name="max_y_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="min_z_nm_${restraintId}">Min Z (nm):</label>
                    <input type="number" id="min_z_nm_${restraintId}" name="min_z_nm_${restraintId}" step="0.01">
                </div>
                <div class="form-group">
                    <label for="max_z_nm_${restraintId}">Max Z (nm):</label>
                    <input type="number" id="max_z_nm_${restraintId}" name="max_z_nm_${restraintId}" step="0.01">
                </div>
                ${forceConstantField}
                ${orderField}
            `;
        } else if (type === 'vec_restraint') {
            return `
                <div class="form-group">
                    <label for="vec_grp1_${restraintId}">Vector Group 1:</label>
                    <input type="text" id="vec_grp1_${restraintId}" name="vec_grp1_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="vec_grp2_${restraintId}">Vector Group 2:</label>
                    <input type="text" id="vec_grp2_${restraintId}" name="vec_grp2_${restraintId}">
                </div>
                <div class="form-group">
                    <label for="pos_ref1_nm_${restraintId}">Position Reference 1 (nm):</label>
                    <input type="text" id="pos_ref1_nm_${restraintId}" name="pos_ref1_nm_${restraintId}" placeholder="x,y,z">
                </div>
                <div class="form-group">
                    <label for="pos_ref2_nm_${restraintId}">Position Reference 2 (nm):</label>
                    <input type="text" id="pos_ref2_nm_${restraintId}" name="pos_ref2_nm_${restraintId}" placeholder="x,y,z">
                </div>
                ${forceConstantField}
            `;
        } else if (type === 'test') {
            return `
                <div class="form-group">
                    <label for="test_param_${restraintId}">Test Parameter:</label>
                    <input type="text" id="test_param_${restraintId}" name="test_param_${restraintId}">
                </div>
                ${forceConstantField}
            `;
        }
    }

    // Function to add a new restraint
    // Function to reindex restraints after adding or removing
    function reindexRestraints() {
        const restraintItems = document.querySelectorAll('.restraint-item');
        restraintCounter = restraintItems.length;
        
        // Update each restraint item with the correct index
        restraintItems.forEach((item, index) => {
            const newIndex = index + 1;
            
            // Update the heading
            const heading = item.querySelector('h3');
            if (heading) {
                heading.textContent = `Restraint ${newIndex}`;
            }
            
            // Update data-id attribute on remove button
            const removeBtn = item.querySelector('.remove-restraint');
            if (removeBtn) {
                removeBtn.setAttribute('data-id', newIndex);
            }
            
            // Update all input IDs, names, and labels
            const inputs = item.querySelectorAll('input, select');
            inputs.forEach(input => {
                const oldId = input.id;
                const baseName = oldId.split('_').slice(0, -1).join('_');
                const newId = `${baseName}_${newIndex}`;
                
                // Update input ID and name
                input.id = newId;
                input.name = newId;
                
                // Update associated label
                const label = item.querySelector(`label[for="${oldId}"]`);
                if (label) {
                    label.setAttribute('for', newId);
                }
            });
            
            // Update restraint fields container ID
            const fieldsContainer = item.querySelector('[id^="restraint_fields_"]');
            if (fieldsContainer) {
                fieldsContainer.id = `restraint_fields_${newIndex}`;
            }
            
            // Re-attach event listener to type select
            const typeSelect = item.querySelector(`[id^="restr_type_"]`);
            if (typeSelect) {
                // Clone and replace to remove old event listeners
                const newTypeSelect = typeSelect.cloneNode(true);
                typeSelect.parentNode.replaceChild(newTypeSelect, typeSelect);
                
                // Add new event listener
                newTypeSelect.addEventListener('change', function() {
                    const fieldsContainer = document.getElementById(`restraint_fields_${newIndex}`);
                    fieldsContainer.innerHTML = createRestraintFields(newIndex, this.value);
                });
            }
        });
    }
    
    function addRestraint() {
        restraintCounter++;
        
        const restraintItem = document.createElement('div');
        restraintItem.className = 'restraint-item';
        restraintItem.innerHTML = `
            <button type="button" class="remove-restraint" data-id="${restraintCounter}">×</button>
            <h3>Restraint ${restraintCounter}</h3>
            <div class="form-group">
                <label for="restr_name_${restraintCounter}">Name:</label>
                <input type="text" id="restr_name_${restraintCounter}" name="restr_name_${restraintCounter}" required>
            </div>
            <div class="form-group">
                <label for="restr_type_${restraintCounter}">Type:</label>
                <select id="restr_type_${restraintCounter}" name="restr_type_${restraintCounter}" required>
                    <option value="sphere">Sphere</option>
                    <option value="funnel">Funnel</option>
                    <option value="distance">Distance</option>
                    <option value="angle">Angle</option>
                    <option value="dihedral">Dihedral</option>
                    <option value="ref_file">Reference File</option>
                    <option value="dist_ref_position">Distance Reference Position</option>
                    <option value="xyz_box">XYZ Box</option>
                    <option value="vec_restraint">Vector Restraint</option>
                    <option value="test">Test</option>
                </select>
            </div>
            <div id="restraint_fields_${restraintCounter}">
                ${createRestraintFields(restraintCounter, 'distance')}
            </div>
        `;
        
        restraintsContainer.appendChild(restraintItem);
        
        // Add event listener to the remove button
        const removeBtn = restraintItem.querySelector('.remove-restraint');
        removeBtn.addEventListener('click', function() {
            // Get the parent restraint item directly from the button
            const restraintToRemove = this.closest('.restraint-item');
            if (restraintToRemove) {
                restraintsContainer.removeChild(restraintToRemove);
                // Reindex remaining restraints
                reindexRestraints();
                // Regenerate YAML to reflect the changes
                generateYaml();
            }
        });
        
        // Add event listener to the type select to update fields
        const typeSelect = document.getElementById(`restr_type_${restraintCounter}`);
        typeSelect.addEventListener('change', function() {
            const fieldsContainer = document.getElementById(`restraint_fields_${restraintCounter}`);
            fieldsContainer.innerHTML = createRestraintFields(restraintCounter, this.value);
        });
    }
    
    // Function to generate YAML
    function generateYaml() {
        // Create the YAML structure
        const yamlObj = {
            method: document.getElementById('method').value,
            
            integrator: {
                dt: parseFloat(document.getElementById('dt').value),
                friction_coeff: parseFloat(document.getElementById('friction_coeff').value)
            },
            
            temperature: parseInt(document.getElementById('temperature').value),
            seed: parseInt(document.getElementById('seed').value),
            
            input_files: {
                complex: document.getElementById('complex').value,
                system: document.getElementById('system').value,
                ligands: document.getElementById('ligands').value
            },
            
            output: {
                output_dir: document.getElementById('output_dir').value,
                report_restraint: document.getElementById('report_restraint').checked,
                report_interval: parseInt(document.getElementById('report_interval').value),
                trajectory_interval: parseInt(document.getElementById('trajectory_interval').value),
                checkpoint_interval: parseInt(document.getElementById('checkpoint_interval').value)
            }
        };
        
        // Add method-specific parameters
        if (yamlObj.method === 'min') {
            // For minimization, add min_params instead of steps and continue_md
            yamlObj.min_params = {
                tolerance: parseFloat(document.getElementById('min_tolerance').value),
                maxiter: parseInt(document.getElementById('min_maxiter').value)
            };
        } else {
            // For other methods (eq, metadynamics), add continue_md and steps
            yamlObj.continue_md = document.getElementById('continue_md').checked;
            yamlObj.steps = parseInt(document.getElementById('steps').value);
            
            // Add barostat settings for non-minimization methods
            yamlObj.barostat = {
                frequency: parseInt(document.getElementById('barostat_frequency').value),
                pressure: parseFloat(document.getElementById('pressure').value)
            };
        }
        
        // Add metadynamics settings if method is 'metadynamics'
        if (yamlObj.method === 'metadynamics') {
            yamlObj.meta_set = {
                biasFactor: 30,
                height: 1.5,
                frequency: 50000
            };
            
            // Process collective variables only if the include_colvars checkbox is checked
            const includeColvars = document.getElementById('include_colvars').checked;
            const colvarItems = document.querySelectorAll('.colvar-item');
            if (includeColvars && colvarItems.length > 0) {
                yamlObj.colvars = {};
                
                colvarItems.forEach((item, index) => {
                    const colvarId = index + 1;
                    const nameElement = document.getElementById(`colvar_name_${colvarId}`);
                    if (!nameElement) return; // Skip if element doesn't exist
                    
                    const colvarName = nameElement.value;
                    const colvarType = document.getElementById(`colvar_type_${colvarId}`).value;
                    
                    const colvar = {
                        type: colvarType
                    };
                    
                    try {
                        if (colvarType === 'distance') {
                            colvar.grp1_idx = document.getElementById(`grp1_idx_${colvarId}`)?.value;
                            colvar.grp2_idx = document.getElementById(`grp2_idx_${colvarId}`)?.value;
                            colvar.min_cv_nm = parseFloat(document.getElementById(`min_cv_nm_${colvarId}`)?.value || 0);
                            colvar.max_cv_nm = parseFloat(document.getElementById(`max_cv_nm_${colvarId}`)?.value || 0);
                            colvar.bins = parseInt(document.getElementById(`bins_${colvarId}`)?.value || 200);
                            colvar.biasWidth_nm = parseFloat(document.getElementById(`biasWidth_nm_${colvarId}`)?.value || 0.2);
                        } else if (colvarType === 'dihedral') {
                            colvar.grp1_idx = document.getElementById(`grp1_idx_${colvarId}`)?.value;
                            colvar.grp2_idx = document.getElementById(`grp2_idx_${colvarId}`)?.value;
                            colvar.grp3_idx = document.getElementById(`grp3_idx_${colvarId}`)?.value;
                            colvar.grp4_idx = document.getElementById(`grp4_idx_${colvarId}`)?.value;
                            colvar.min_cv_degree = parseFloat(document.getElementById(`min_cv_degree_${colvarId}`)?.value || 0);
                            colvar.max_cv_degree = parseFloat(document.getElementById(`max_cv_degree_${colvarId}`)?.value || 0);
                            colvar.bins = parseInt(document.getElementById(`bins_${colvarId}`)?.value || 200);
                            colvar.biasWidth_degree = parseFloat(document.getElementById(`biasWidth_nm_${colvarId}`)?.value || 0.2);
                        } else if (colvarType === 'angle') {
                            colvar.grp1_idx = document.getElementById(`grp1_idx_${colvarId}`)?.value;
                            colvar.grp2_idx = document.getElementById(`grp2_idx_${colvarId}`)?.value;
                            colvar.grp3_idx = document.getElementById(`grp3_idx_${colvarId}`)?.value;
                            colvar.min_cv_degree = parseFloat(document.getElementById(`min_cv_degree_${colvarId}`)?.value || 0);
                            colvar.max_cv_degree = parseFloat(document.getElementById(`max_cv_degree_${colvarId}`)?.value || 0);
                            colvar.bins = parseInt(document.getElementById(`bins_${colvarId}`)?.value || 200);
                            colvar.biasWidth_degree = parseFloat(document.getElementById(`biasWidth_nm_${colvarId}`)?.value || 0.2);
                        } else if (colvarType === 'min_distances') {
                            colvar.grp1_idx = document.getElementById(`grp1_idx_${colvarId}`)?.value;
                            colvar.grp2_idx = document.getElementById(`grp2_idx_${colvarId}`)?.value;
                            colvar.min_cv_nm = parseFloat(document.getElementById(`min_cv_nm_${colvarId}`)?.value || 0);
                            colvar.max_cv_nm = parseFloat(document.getElementById(`max_cv_nm_${colvarId}`)?.value || 0);
                            colvar.num_pairs = parseInt(document.getElementById(`num_pairs_${colvarId}`)?.value || 1);
                            colvar.bins = parseInt(document.getElementById(`bins_${colvarId}`)?.value || 200);
                            colvar.biasWidth_nm = parseFloat(document.getElementById(`biasWidth_nm_${colvarId}`)?.value || 0.2);
                        } else if (colvarType === 'distance_ref') {
                            colvar.grp1_idx = document.getElementById(`grp1_idx_${colvarId}`)?.value;
                            colvar.ref_position_nm = document.getElementById(`ref_position_nm_${colvarId}`)?.value;
                            colvar.min_cv_nm = parseFloat(document.getElementById(`min_cv_nm_${colvarId}`)?.value || 0);
                            colvar.max_cv_nm = parseFloat(document.getElementById(`max_cv_nm_${colvarId}`)?.value || 0);
                            colvar.bins = parseInt(document.getElementById(`bins_${colvarId}`)?.value || 200);
                            colvar.biasWidth_nm = parseFloat(document.getElementById(`biasWidth_nm_${colvarId}`)?.value || 0.2);
                        } else if (colvarType === 'rmsd') {
                            colvar.grp1_idx = document.getElementById(`grp1_idx_${colvarId}`)?.value;
                            colvar.ref_file = document.getElementById(`ref_file_${colvarId}`)?.value;
                            colvar.min_cv_nm = parseFloat(document.getElementById(`min_cv_nm_${colvarId}`)?.value || 0);
                            colvar.max_cv_nm = parseFloat(document.getElementById(`max_cv_nm_${colvarId}`)?.value || 0);
                            colvar.bins = parseInt(document.getElementById(`bins_${colvarId}`)?.value || 200);
                            colvar.biasWidth_nm = parseFloat(document.getElementById(`biasWidth_nm_${colvarId}`)?.value || 0.2);
                        }
                        
                        yamlObj.colvars[colvarName] = colvar;
                    } catch (error) {
                        console.error(`Error processing collective variable ${colvarId}:`, error);
                    }
                });
                
                // If no valid colvars were added, remove the empty colvars object
                if (Object.keys(yamlObj.colvars).length === 0) {
                    delete yamlObj.colvars;
                }
            } else {
                // If colvars are not included, add an empty colvars object
                yamlObj.colvars = {};
            }
        }
        
        // Process restraints only if the include_restraints checkbox is checked
        const includeRestraints = document.getElementById('include_restraints').checked;
        const restraintItems = document.querySelectorAll('.restraint-item');
        if (includeRestraints && restraintItems.length > 0) {
            yamlObj.restraint = {};
            
            restraintItems.forEach((item, index) => {
                const restraintId = index + 1;
                const nameElement = document.getElementById(`restr_name_${restraintId}`);
                if (!nameElement) return; // Skip if element doesn't exist
                
                const restraintName = nameElement.value;
                const restraintType = document.getElementById(`restr_type_${restraintId}`).value;
                
                const restraint = {
                    type: restraintType
                };
                
                try {
                    if (restraintType === 'sphere') {
                        restraint.restr_grp = document.getElementById(`restr_grp_${restraintId}`)?.value;
                        restraint.cent_grp = document.getElementById(`cent_grp_${restraintId}`)?.value;
                        restraint.radius_nm = parseFloat(document.getElementById(`radius_nm_${restraintId}`)?.value || 0);
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                    } else if (restraintType === 'funnel') {
                        restraint.restr_grp = document.getElementById(`restr_grp_${restraintId}`)?.value;
                        restraint.gate_grp = document.getElementById(`gate_grp_${restraintId}`)?.value;
                        restraint.pocket_grp = document.getElementById(`pocket_grp_${restraintId}`)?.value;
                        restraint.upper_wall_nm = parseFloat(document.getElementById(`upper_wall_nm_${restraintId}`)?.value || 0);
                        restraint.lower_wall_nm = parseFloat(document.getElementById(`lower_wall_nm_${restraintId}`)?.value || 0);
                        restraint.width = parseFloat(document.getElementById(`width_${restraintId}`)?.value || 0);
                        restraint.buffer = parseFloat(document.getElementById(`buffer_${restraintId}`)?.value || 0);
                        restraint.steepness = parseFloat(document.getElementById(`steepness_${restraintId}`)?.value || 0);
                        restraint.s_center = parseFloat(document.getElementById(`s_center_${restraintId}`)?.value || 0);
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                    } else if (restraintType === 'distance') {
                        restraint.grp1 = document.getElementById(`grp1_${restraintId}`)?.value;
                        restraint.grp2 = document.getElementById(`grp2_${restraintId}`)?.value;
                        restraint.min_nm = parseFloat(document.getElementById(`min_nm_${restraintId}`)?.value || 0);
                        restraint.max_nm = parseFloat(document.getElementById(`max_nm_${restraintId}`)?.value || 0);
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                        restraint.order = parseInt(document.getElementById(`order_${restraintId}`)?.value || 2);
                    } else if (restraintType === 'angle') {
                        restraint.grp1 = document.getElementById(`grp1_${restraintId}`)?.value;
                        restraint.grp2 = document.getElementById(`grp2_${restraintId}`)?.value;
                        restraint.grp3 = document.getElementById(`grp3_${restraintId}`)?.value;
                        restraint.min_degree = parseFloat(document.getElementById(`min_degree_${restraintId}`)?.value || 0);
                        restraint.max_degree = parseFloat(document.getElementById(`max_degree_${restraintId}`)?.value || 0);
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                        restraint.order = parseInt(document.getElementById(`order_${restraintId}`)?.value || 2);
                    } else if (restraintType === 'dihedral') {
                        restraint.grp1 = document.getElementById(`grp1_${restraintId}`)?.value;
                        restraint.grp2 = document.getElementById(`grp2_${restraintId}`)?.value;
                        restraint.grp3 = document.getElementById(`grp3_${restraintId}`)?.value;
                        restraint.grp4 = document.getElementById(`grp4_${restraintId}`)?.value;
                        restraint.min_degree = parseFloat(document.getElementById(`min_degree_${restraintId}`)?.value || 0);
                        restraint.max_degree = parseFloat(document.getElementById(`max_degree_${restraintId}`)?.value || 0);
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                        restraint.order = parseInt(document.getElementById(`order_${restraintId}`)?.value || 2);
                    } else if (restraintType === 'ref_file') {
                        restraint.ref_file = document.getElementById(`ref_file_${restraintId}`)?.value;
                        restraint.restr_grp = document.getElementById(`restr_grp_${restraintId}`)?.value;
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                    } else if (restraintType === 'dist_ref_position') {
                        restraint.restr_grp = document.getElementById(`restr_grp_${restraintId}`)?.value;
                        restraint.ref_position_nm = document.getElementById(`ref_position_nm_${restraintId}`)?.value;
                        restraint.min_nm = parseFloat(document.getElementById(`min_nm_${restraintId}`)?.value || 0);
                        restraint.max_nm = parseFloat(document.getElementById(`max_nm_${restraintId}`)?.value || 0);
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                        restraint.order = parseInt(document.getElementById(`order_${restraintId}`)?.value || 2);
                    } else if (restraintType === 'xyz_box') {
                        restraint.restr_grp = document.getElementById(`restr_grp_${restraintId}`)?.value;
                        restraint.min_x_nm = parseFloat(document.getElementById(`min_x_nm_${restraintId}`)?.value || 0);
                        restraint.max_x_nm = parseFloat(document.getElementById(`max_x_nm_${restraintId}`)?.value || 0);
                        restraint.min_y_nm = parseFloat(document.getElementById(`min_y_nm_${restraintId}`)?.value || 0);
                        restraint.max_y_nm = parseFloat(document.getElementById(`max_y_nm_${restraintId}`)?.value || 0);
                        restraint.min_z_nm = parseFloat(document.getElementById(`min_z_nm_${restraintId}`)?.value || 0);
                        restraint.max_z_nm = parseFloat(document.getElementById(`max_z_nm_${restraintId}`)?.value || 0);
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                        restraint.order = parseInt(document.getElementById(`order_${restraintId}`)?.value || 2);
                    } else if (restraintType === 'vec_restraint') {
                        restraint.vec_grp1 = document.getElementById(`vec_grp1_${restraintId}`)?.value;
                        restraint.vec_grp2 = document.getElementById(`vec_grp2_${restraintId}`)?.value;
                        restraint.pos_ref1_nm = document.getElementById(`pos_ref1_nm_${restraintId}`)?.value;
                        restraint.pos_ref2_nm = document.getElementById(`pos_ref2_nm_${restraintId}`)?.value;
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                    } else if (restraintType === 'test') {
                        restraint.test_param = document.getElementById(`test_param_${restraintId}`)?.value;
                        restraint.restr_k = parseInt(document.getElementById(`restr_k_${restraintId}`)?.value || 0);
                    }
                    
                    yamlObj.restraint[restraintName] = restraint;
                } catch (error) {
                    console.error(`Error processing restraint ${restraintId}:`, error);
                }
            });
            
            // If no valid restraints were added, remove the empty restraint object
            if (Object.keys(yamlObj.restraint).length === 0) {
                delete yamlObj.restraint;
            }
        }
        
        // Convert to YAML and display
        const yamlText = jsyaml.dump(yamlObj, { lineWidth: -1 });
        yamlResult.textContent = yamlText;
    }
    
    // Function to download YAML
    function downloadYaml() {
        if (!yamlResult.textContent) {
            alert('Please generate YAML first!');
            return;
        }
        
        const yamlContent = yamlResult.textContent;
        const blob = new Blob([yamlContent], { type: 'text/yaml' });
        const url = URL.createObjectURL(blob);
        
        const methodValue = document.getElementById('method').value;
        const filename = `${methodValue}_config.yaml`;
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
});
